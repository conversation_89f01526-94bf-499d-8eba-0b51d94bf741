# KKTV-15431 Implementation Plan: LineTV Manifest API

## 任務概述
實作一支 API 讓 vendor LINETV 可以獲得播放 DRM 的相關 streaming 資源和授權 token (playback token)。

## API 規格
- **路徑**: `POST /vendors/linetv/episodes/{episodeID}/manifest`
- **驗證**: BasicAuth (server-to-server)
- **Handler 位置**: `kktvapi/internal/vendors/linetv/`

## 詳細實作計劃

### 1. 分析現有程式碼結構

#### 1.1 現有 manifest API 分析
- **位置**: `kktvapi/internal/v3/manifest/handler.go`
- **核心組件**: `legacyHelper` interface 和 `legacy` struct
- **關鍵方法**: `GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool)`

#### 1.2 現有 playback token API 分析
- **位置**: `kkapp/kkhandler/playback_token.go`
- **核心函數**: `playback.NewPlayback()` 和 `playback.NewToken()`
- **模型**: `playback.PlaybackRequest` 和 `playback.Playback`

#### 1.3 BasicAuth 機制分析
- **位置**: `kktvapi/internal/pkg/middleware/appauth/appauth.go`
- **方法**: `BasicAuth(next http.Handler) http.Handler`
- **驗證流程**: 使用 `authedAppRepo.GetActiveByAppID()` 驗證 app credentials

### 2. 需要修改的檔案清單

#### 2.1 新增檔案
- `kktvapi/internal/vendors/linetv/handler.go` - 主要 handler 實作
- `kktvapi/internal/vendors/linetv/handler_test.go` - 單元測試
- `kktvapi/internal/vendors/linetv/request.go` - 請求結構定義
- `kktvapi/internal/vendors/linetv/response.go` - 回應結構定義
- `kktvapi/internal/vendors/linetv/http_error.go` - 錯誤定義
- `kkapp/model/playback/legacy_helper.go` - playback token legacy helper

#### 2.2 修改檔案
- `kktvapi/internal/v3/manifest/legacy.go` - 將 `legacyHelper` interface 改為 public
- `kktvapi/internal/vendors/container.go` - 新增 LineTV handler
- `kktvapi/internal/v3/manifest/legacy.go` - 新增 `GetManifestsByTargetFile` 方法

### 3. 實作步驟

#### 步驟 1: 修改 manifest legacy helper
1. 將 `legacyHelper` interface 改名為 `LegacyHelper` (public)
2. 新增 `GetManifestsByTargetFile` 方法到 interface
3. 實作 `GetManifestsByTargetFile` 方法

#### 步驟 2: 建立 playback token legacy helper
1. 在 `kkapp/model/playback/` 建立 `LegacyHelper` interface
2. 實作 `GetToken(userID, deviceID string, pb PlaybackRequest) (*Playback, error)` 方法
3. 方法內部呼叫 `NewToken()` 和 `Save()`

#### 步驟 3: 實作 LineTV handler
1. 建立 `Handler` struct 包含必要的 dependencies
2. 實作 `PostEpisodeManifest` 方法
3. 處理請求參數驗證
4. 呼叫 manifest legacy helper 取得 streaming assets
5. 呼叫 playback token legacy helper 取得 playback token
6. 組合回應資料

#### 步驟 4: 建立支援檔案
1. 定義請求和回應的資料結構
2. 定義錯誤訊息
3. 建立 constructor 函數

#### 步驟 5: 更新 vendor container
1. 在 `vendors/container.go` 新增 LineTV handler
2. 確保正確的 dependency injection

#### 步驟 6: 路由設定
1. 在適當的路由檔案中新增 LineTV manifest API 路由
2. 套用 BasicAuth middleware

#### 步驟 7: 撰寫測試
1. 建立完整的單元測試
2. 測試正常情況 (p4 quality)
3. 測試錯誤情況 (無效 quality 如 p6)
4. 測試驗證機制

### 4. 關鍵技術挑戰與解決方案

#### 4.1 targetFile 處理差異
**挑戰**: 原 manifest API 根據 user 身份判斷 targetFile，但 LineTV API 需直接按 quality 參數決定

**解決方案**: 
- 新增 `GetManifestsByTargetFile` 方法，直接接受 targetFile 參數
- 在 LineTV handler 中直接將 quality 轉換為對應的 targetFile (p0-p5)

#### 4.2 Quality 參數驗證
**挑戰**: 需要驗證 quality 參數是否為有效值 (p0-p5)

**解決方案**:
- 建立 quality 驗證函數
- 無效 quality 回傳 400 錯誤

#### 4.3 Server-to-Server 驗證
**挑戰**: 需要使用 BasicAuth 而非一般的 user token

**解決方案**:
- 使用現有的 `appauth.BasicAuth` middleware
- 確保 LineTV 的 app credentials 已在資料庫中設定

### 5. 驗收標準實作

#### 5.1 正常情況測試
- 請求 p4 quality
- 驗證回應包含 dash url、hls url 和 p4 download path
- 驗證 playback token 正確產生

#### 5.2 錯誤情況測試
- 請求無效 quality (p6)
- 驗證回傳 400 錯誤和適當錯誤訊息

### 6. 後續工作
1. 確保 LineTV app credentials 在資料庫中正確設定
2. 進行整合測試
3. 更新 API 文件
4. 部署到測試環境進行驗證

## 風險評估
- **低風險**: 大部分功能可重用現有程式碼
- **中風險**: targetFile 處理邏輯需要仔細測試
- **注意事項**: 確保不影響現有 manifest API 功能
