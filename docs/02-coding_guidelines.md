# Coding Style and Structure Guidelines

This document outlines the coding style and structure guidelines for our project, with a focus on API handler functions, clean architecture, object-oriented programming (OOP), and SOLID principles.

## Comments
- do not add comments to the code actively

## API Handler Function Composition

The `handler.go` file in `kktvapi/internal/vendors/feversocial` provides an excellent example of how to compose API handler functions. Below are the key patterns and practices to follow:

### Handler Structure

```go
type Handler struct {
    // Dependencies are injected here
    userRepo        user.UserRepository
    userCacheReader cache.Cacher
    clock           clock.Clock
}
```

### Constructor Pattern

```go
func NewFeverSocialHandler() *Handler {
    return &Handler{
        userRepo:        user.NewUserRepository(),
        userCacheReader: cache.New(container.CachePoolUser().Slave()),
        clock:           clock.New(),
    }
}
```

### Handler Function Pattern

Each handler function should follow this general pattern:

1. **Parameter Validation**: Validate input parameters first
2. **Error Handling**: Handle errors explicitly and return appropriate HTTP responses
3. **Business Logic**: Implement the core business logic
4. **Response Rendering**: Use the render package to return consistent responses

Example:

```go
func (h *Handler) GetUserByCode(w http.ResponseWriter, r *http.Request) {
    // 1. Parameter Validation
    var code string
    if urlValues, err := httpreq.ParseQuery(r); err != nil {
        render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
        return
    } // assign code ....

    // 2. Business Logic with Error Handling
    var userID string
    cKey := key.UserAuthCode(code)
    if err := h.userCacheReader.Get(cKey, &userID); errors.Is(err, cache.ErrCacheMiss) {
        render.JSONNotFound(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
        return
    } else if err != nil {
        log.Error("feversocialHandler: GetUserByCode: failed to userCacheReader.Get").Str("cache_key", cKey).Err(err).Send()
        render.JSONInternalServerErr(w, ErrUnknownError)
        return
    }

    // 3. More Business Logic
    u, err := h.userRepo.GetActiveByID(userID)
    if err != nil {
        log.Error("feversocialHandler: GetUserByCode: failed to userRepo.GetActiveByID").Str("user_id", userID).Err(err).Send()
        render.JSONInternalServerErr(w, ErrUnknownError)
        return
    } else if u == nil {
        render.JSONNotFound(w, ErrInvalidUser)
        return
    }

    // 4. Response Rendering
    render.JSONOk(w, getUserResp{
        ID:    encryptUserID(u.ID),
        Name:  u.GetDisplayName(),
        Email: u.Email.ValueOrZero(),
        Phone: u.Phone.ValueOrZero(),
    })
}
```

### Logging Pattern

Use structured logging with context:
- use Error level for the unknown exceptions
- the topic should have followed the pattern of `<handler_name>: <function_name>: <error_from_where>`

```
log.Error("feversocialHandler: GetUserByCode: failed to userCacheReader.Get").
    Str("cache_key", cKey).
    Err(err).
    Send()
```

## RESTful API Error Code Conventions

All API error responses **must** include an `error` object containing a standardized `code` and a descriptive `message`. This ensures consistent error handling across services and simplifies frontend integration.

### Error Response Format

```json
{
  "error": {
    "code": "400.0",
    "message": "Invalid request"
  }
}
```

- The `code` is a string composed of:
  - The **HTTP status code** (e.g., `400`, `403`, `404`, `500`)
  - A **sub-code** separated by a dot (`.`) to indicate a more specific error category

### Error Code Examples

| Code     | Meaning                                 |
|----------|-----------------------------------------|
| `"400.0"` | Invalid request parameters              |
| `"403.0"` | Permission denied                       |
| `"404.1"` | Resource not found (e.g., episode)      |
| `"404.2"` | Resource not found (e.g., video)        |
| `"500.0"` | Unknown server error                    |

---

### Centralized Error Definitions

All error variables **must be defined centrally** within the corresponding module's `http_error.go` file. This promotes consistency, maintainability, and easier updates.

Example:

```go
var (
    ErrUnknown           = &rest.Err{Code: "500.0", Message: "Unknown error"}
    ErrInvalidRequest    = &rest.Err{Code: "400.0", Message: "Invalid request"}
    ErrEpisodeNotFound   = &rest.Err{Code: "404.1", Message: "Episode not found"}
    ErrTitleNotFound     = &rest.Err{Code: "404.2", Message: "Title not found"}
    ErrPermissionDenied  = &rest.Err{Code: "403.0", Message: "Permission denied"}
)
```

- **Naming conventions**: Use clear, consistent variable names prefixed with `Err`.
- **Idiomatic Go style**: Group related errors in a single `var` block.
- **Comments** (optional): Add inline comments for complex or less obvious errors.

---

### Best Practices & Rationale

- **Consistent error schema** simplifies frontend error handling and internationalization.
- **Centralized definitions** reduce duplication, making maintenance easier.
- **Hierarchical error codes** (`HTTPStatus.SubCode`) allow precise error categorization.
- **Extendability**: New sub-codes can be added without breaking existing clients.
- **Edge cases**:
  - Always return a valid `error` object, even for unexpected failures.
  - Use `"500.0"` for unknown or unhandled errors.
  - Avoid exposing sensitive internal details in error messages.

---

## Clean Architecture

Our codebase follows Clean Architecture principles, which separate concerns into distinct layers:

### Layers

1. **Entities/Domain Layer**: Core business logic and entities.
   - folder: `pkg/model`
2. **Use Case/Application Layer**: Application-specific business rules
3. **Interface Adapters Layer**: Controllers, presenters, and gateways
4. **Frameworks & Drivers Layer**: Web, DB, UI, etc.

### Dependency Rule

Dependencies should only point inward. Outer layers can depend on inner layers, but inner layers should not depend on outer layers.

In our codebase:
- The `Handler` struct is part of the Interface Adapters layer
- It depends on repositories and services from the Use Case layer
- It does not expose implementation details to the outer layers

## Object-Oriented Programming (OOP)

Our Go codebase follows OOP principles adapted for Go's approach:

### Encapsulation

- Use structs with private fields and public methods
- Expose functionality through interfaces

Example:
```
type Handler struct {
    // Private fields
    userRepo        user.UserRepository
    authedAppRepo   appauth.Repository
    // ...
}

// Public methods
func (h *Handler) GetUserByCode(w http.ResponseWriter, r *http.Request) {
    // ...
}
```

### Inheritance (Composition in Go)

Go uses composition instead of inheritance:

```
type BaseHandler struct {
    // Common fields
}

type SpecializedHandler struct {
    BaseHandler
    // Additional fields
}
```

### Polymorphism

Use interfaces to achieve polymorphism:

```
type Repository interface {
    Get(id string) (Entity, error)
    // ...
}

// Different implementations can satisfy this interface
```

## SOLID Principles

### Single Responsibility Principle (SRP)

Each struct or function should have only one reason to change.

Example: The `Handler` struct is responsible only for handling HTTP requests, while repositories are responsible for data access.

### Open/Closed Principle (OCP)

Software entities should be open for extension but closed for modification.

Example: Adding new handler methods doesn't require modifying existing ones.

### Liskov Substitution Principle (LSP)

Objects should be replaceable with instances of their subtypes without altering program correctness.

Example: Different implementations of the `Repository` interface can be used interchangeably.

### Interface Segregation Principle (ISP)

Clients should not be forced to depend on interfaces they don't use.

Example: Specific interfaces like `UserRepository` instead of a generic `Repository`.

### Dependency Inversion Principle (DIP)

High-level modules should not depend on low-level modules. Both should depend on abstractions.

Example: The `Handler` depends on the `UserRepository` interface, not on a specific implementation.

## Unit Testing Patterns

The `handler_test.go` file in `kktvapi/internal/vendors/feversocial` provides an excellent example of how to structure and write unit tests for API handlers. Below are the key patterns and practices to follow:

### Test Suite Structure

Use the testify/suite package to organize tests into suites:

```
type FeverSocialHandlerSuite struct {
    suite.Suite
    ctrl                *gomock.Controller
    mockUserRepo        *user.MockUserRepository
    mockClock           *clock.MockClock

    handler Handler
    app     *bone.Mux
}

func TestFeverSocialHandlerSuite(t *testing.T) {
    suite.Run(t, new(FeverSocialHandlerSuite))
}
```

### Setup and Teardown

Use SetupTest and TearDownTest methods to initialize and clean up test dependencies:

```
func (suite *FeverSocialHandlerSuite) SetupTest() {
    suite.ctrl = gomock.NewController(suite.T())
    suite.mockUserRepo = user.NewMockUserRepository(suite.ctrl)
    suite.mockClock = clock.NewMockClock(suite.ctrl)
    suite.handler = Handler{
        userRepo:        suite.mockUserRepo,
        userCacheReader: suite.mockUserCacheReader,
        clock:           suite.mockClock,
    }

    suite.app = bone.New()
    suite.app.GetFunc("/vendors/feversocial/get-user", suite.handler.GetUserByCode)
}

func (suite *FeverSocialHandlerSuite) TearDownTest() {
    // Clean up resources if needed
}
```

### Table-Driven Tests

Use table-driven tests to test multiple scenarios with a single test function:

```
func (suite *FeverSocialHandlerSuite) TestGetUserByCode() {
    testcases := []struct {
        name           string
        queryStrings   string
        given          func() (wait chan struct{})
        expectedStatus int
        assertBody     func(body []byte)
    }{
        {
            name:         "WHEN code not found in cache THEN return 404",
            queryStrings: "code=encodedjosie",
            given: func() chan struct{} {
                suite.mockUserCodeNotFound("encodedjosie")
                return nil
            },
            expectedStatus: http.StatusNotFound,
        },
        {
            name:           "WHEN code is not given THEN return 400",
            queryStrings:   "",
            expectedStatus: http.StatusBadRequest,
        },
        // More test cases...
    }

    for _, tc := range testcases {
        suite.Run(tc.name, func() {
            if tc.given != nil {
                tc.given()
            }

            req := httptest.NewRequest(http.MethodGet, "/vendors/feversocial/get-user?"+tc.queryStrings, nil)
            rr := httptest.NewRecorder()
            suite.app.ServeHTTP(rr, req)

            suite.Equal(tc.expectedStatus, rr.Code)
            if tc.assertBody != nil {
                tc.assertBody(rr.Body.Bytes())
            }
        })
    }
}
```

### Mock Helper Methods

Use mockgen to set up common mock scenarios. 

#### Mockgen command

For each interface that needs to be mocked, the mockgen command should be placed in the file top that you want to generate the mock.

```
//go:generate mockgen -source={filename}.go -destination={filename}__mock.go -package=feversocial
package feversocial

type Handler struct {
    userRepo        user.UserRepository
}
```

### HTTP Testing

Use httptest package to simulate HTTP requests and responses:

```
req := httptest.NewRequest(http.MethodGet, "/vendors/feversocial/get-user?"+tc.queryStrings, nil)
rr := httptest.NewRecorder()
suite.app.ServeHTTP(rr, req)

suite.Equal(tc.expectedStatus, rr.Code)
```

### Assertions

Use testify's assertion methods for clear and consistent assertions:

```
suite.Equal(tc.expectedStatus, rr.Code)
suite.NoError(json.Unmarshal(body, &res))
```

### Asynchronous Testing

Use channels to test asynchronous operations:

```
func (suite *FeverSocialHandlerSuite) mockDeletedUserCodeFromCache(code string) chan struct{} {
    wait := make(chan struct{})
    cKey := key.UserAuthCode(code)
    suite.mockUserCacheWriter.EXPECT().Del(cKey).Return(nil).Do(func(string) {
        close(wait)
    })
    return wait
}

// In the test:
wait := tc.given()
// ... perform test actions ...
if wait != nil {
    <-wait // Wait for async operation to complete
}
```

### Test Naming Convention

Use descriptive test names that follow a "WHEN...THEN..." pattern:

```
"WHEN code not found in cache THEN return 404"
"WHEN code found in cache THEN return user model"
"WHEN code is not given THEN return 400"
```
