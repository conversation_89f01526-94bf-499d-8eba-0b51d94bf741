// Code generated by MockGen. DO NOT EDIT.
// Source: legacy_helper.go

// Package playback is a generated GoMock package.
package playback

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockLegacyHelper is a mock of LegacyHelper interface.
type MockLegacyHelper struct {
	ctrl     *gomock.Controller
	recorder *MockLegacyHelperMockRecorder
}

// MockLegacyHelperMockRecorder is the mock recorder for MockLegacyHelper.
type MockLegacyHelperMockRecorder struct {
	mock *MockLegacyHelper
}

// NewMockLegacyHelper creates a new mock instance.
func NewMockLegacyHelper(ctrl *gomock.Controller) *MockLegacyHelper {
	mock := &MockLegacyHelper{ctrl: ctrl}
	mock.recorder = &MockLegacyHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLegacyHelper) EXPECT() *MockLegacyHelperMockRecorder {
	return m.recorder
}

// GetToken mocks base method.
func (m *MockLegacyHelper) GetToken(userID, deviceID string, pb PlaybackRequest) (*Playback, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToken", userID, deviceID, pb)
	ret0, _ := ret[0].(*Playback)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToken indicates an expected call of GetToken.
func (mr *MockLegacyHelperMockRecorder) GetToken(userID, deviceID, pb interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToken", reflect.TypeOf((*MockLegacyHelper)(nil).GetToken), userID, deviceID, pb)
}
