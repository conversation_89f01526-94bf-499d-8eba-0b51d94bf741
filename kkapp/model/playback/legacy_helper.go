//go:generate mockgen -source legacy_helper.go -destination legacy_helper_mock.go -package playback
package playback

// LegacyHelper provides legacy playback token operations for vendor integrations
type LegacyHelper interface {
	GetToken(userID, deviceID string, pb PlaybackRequest) (*Playback, error)
}

type legacyHelper struct{}

// NewLegacyHelper creates a new LegacyHelper instance
func NewLegacyHelper() LegacyHelper {
	return &legacyHelper{}
}

// GetToken creates a new playback token without permission checks and saves it to storage
// This is designed for server-to-server vendor integrations where permission checks
// are handled at the API level rather than the token level
func (h *legacyHelper) GetToken(userID, deviceID string, pb PlaybackRequest) (*Playback, error) {
	playback := NewToken(userID, deviceID, pb)
	playback.Save()
	return playback, nil
}
