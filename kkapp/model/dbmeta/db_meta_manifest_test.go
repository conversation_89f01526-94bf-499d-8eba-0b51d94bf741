package dbmeta

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSelectTargetFile(t *testing.T) {
	testcases := []struct {
		name         string
		device       string
		quality      string
		purpose      string
		platformStr  string
		expectedFile string
	}{
		{
			name:         "android playback playzone",
			device:       "android",
			quality:      "playzone",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "lowest_bitrate_of_360p_or_p2",
		},
		{
			name:         "android playback low",
			device:       "android",
			quality:      "low",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p2",
		},
		{
			name:         "android playback medium",
			device:       "android",
			quality:      "medium",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "android playback high with android platform",
			device:       "android",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "android",
			expectedFile: "p5",
		},
		{
			name:         "android playback high without android platform",
			device:       "android",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "ios",
			expectedFile: "p4",
		},
		{
			name:         "ios playback playzone",
			device:       "ios",
			quality:      "playzone",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "lowest_bitrate_of_360p_or_p2",
		},
		{
			name:         "ios playback low",
			device:       "ios",
			quality:      "low",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p2",
		},
		{
			name:         "ios playback medium",
			device:       "ios",
			quality:      "medium",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "ios playback high",
			device:       "ios",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p5",
		},
		{
			name:         "web playback playzone",
			device:       "web",
			quality:      "playzone",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "lowest_bitrate_of_360p_or_p2",
		},
		{
			name:         "web playback low",
			device:       "web",
			quality:      "low",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "web playback medium",
			device:       "web",
			quality:      "medium",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p4",
		},
		{
			name:         "web playback high",
			device:       "web",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p5",
		},
		{
			name:         "chromecast playback playzone",
			device:       "chromecast",
			quality:      "playzone",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "lowest_bitrate_of_360p_or_p2",
		},
		{
			name:         "chromecast playback low",
			device:       "chromecast",
			quality:      "low",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "chromecast playback medium",
			device:       "chromecast",
			quality:      "medium",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "chromecast playback high",
			device:       "chromecast",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p5",
		},
		{
			name:         "airplay playback playzone",
			device:       "airplay",
			quality:      "playzone",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "lowest_bitrate_of_360p_or_p2",
		},
		{
			name:         "airplay playback low",
			device:       "airplay",
			quality:      "low",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "airplay playback medium",
			device:       "airplay",
			quality:      "medium",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "airplay playback high",
			device:       "airplay",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "p5",
		},
		{
			name:         "android download medium",
			device:       "android",
			quality:      "medium",
			purpose:      "download",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "android download high",
			device:       "android",
			quality:      "high",
			purpose:      "download",
			platformStr:  "",
			expectedFile: "p4",
		},
		{
			name:         "ios download medium",
			device:       "ios",
			quality:      "medium",
			purpose:      "download",
			platformStr:  "",
			expectedFile: "p3",
		},
		{
			name:         "ios download high",
			device:       "ios",
			quality:      "high",
			purpose:      "download",
			platformStr:  "",
			expectedFile: "p4",
		},
		{
			name:         "unsupported device",
			device:       "unknown",
			quality:      "high",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "",
		},
		{
			name:         "unsupported quality",
			device:       "android",
			quality:      "unknown",
			purpose:      "playback",
			platformStr:  "",
			expectedFile: "",
		},
		{
			name:         "unsupported purpose",
			device:       "android",
			quality:      "high",
			purpose:      "unknown",
			platformStr:  "",
			expectedFile: "",
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			result := SelectTargetFile(tc.device, tc.quality, tc.purpose, tc.platformStr)
			assert.Equal(t, tc.expectedFile, result)
		})
	}
}
