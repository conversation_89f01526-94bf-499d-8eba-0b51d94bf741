package dbmeta

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"gopkg.in/guregu/null.v3"
)

var (
	// this would use in db_meta_episode.go && db_meta_extra.go
	keyManifestFmt = "manifests:v3:%s:hash"
	extraMap       = map[string]bool{
		"tr": true,
		"ot": true,
		"iv": true,
		"bs": true,
	}
)

// type Manifest struct {
// }

type manifestMeta struct {
	ID      string      `db:"id" json:"id"`
	MetaStr null.String `db:"meta" json:"-"` // don't need it in console dashboard
}

// MezzanineFile Mezzanine file
type MezzanineFile struct {
	URL  string  `json:"url"`
	Size float64 `json:"size"`
}

// Mezzanine for api response
type Mezzanine struct {
	EpisodeID         string            `json:"episode_id"`
	DefaultSubtitle   string            `json:"default_subtitle"`
	SubtitleURL       map[string]string `json:"subtitle_url"`
	ThumbnailURL      string            `json:"thumbnail_url,omitempty"`
	ThumbnailSmallURL string            `json:"thumbnail_small_url,omitempty"`
	Dash              MezzanineFile     `json:"dash"`
	Hls               MezzanineFile     `json:"hls"`
	SupportedQuality  []string          `json:"supported_quality"`
}

// Manifest2Redis dump meta_extra and meta_episode table item to redis
func Manifest2Redis(ID string) (i *ExtraRowMeta, outdata []byte, err error) {
	// normal ID 00000338010014
	// trailer ID 00000338tr0014
	// "tr", "ot", "iv", "bs"
	if len(ID) != 14 {
		// not validate ID
		return
	}
	var item manifestMeta
	i = new(ExtraRowMeta)
	db := kkapp.App.DbMeta.Slave()
	extraType := ID[8:10]
	titleID := ID[0:8]
	// log.Println(titleID, extraType)
	if _, ok := extraMap[extraType]; ok {
		// extra title
		err = db.Get(&item, `SELECT id, meta FROM meta_extra WHERE id = $1 ;`, ID)
	} else {
		err = db.Get(&item, `SELECT id, meta FROM meta_episode WHERE id = $1 ;`, ID)
	}

	if err != nil {
		return
	}

	log.Println("[INFO] generate manifest from database", ID)
	if item.MetaStr.Valid {
		key := fmt.Sprintf(keyManifestFmt, titleID)
		// log.Println(key)
		conn := kkapp.App.RedisMeta.Master()
		outdata = []byte(item.MetaStr.String)
		conn.Cmd("HSET", key, ID, outdata)
		err = json.Unmarshal(outdata, i)
	}
	return
}

// ManifestBytes2Redis dump meta_extra and meta_episode table item to redis
func ManifestBytes2Redis(ID string, data []byte) (i *ExtraRowMeta, outdata []byte, err error) {
	if len(ID) != 14 {
		// not validate ID
		return
	}
	outdata = data
	i = new(ExtraRowMeta)
	titleID := ID[0:8]
	log.Println(titleID)
	key := fmt.Sprintf(keyManifestFmt, titleID)
	log.Println(key)
	conn := kkapp.App.RedisMeta.Master()
	conn.Cmd("HSET", key, ID, data)
	err = json.Unmarshal(data, i)
	return
}

func s3Path2CDN(s3Path string) (urlPath string) {
	if strings.HasPrefix(s3Path, "s3://kktv-test-theater") {
		urlPath = strings.Replace(s3Path, "s3://kktv-test-theater", "https://test-theater.kktv.com.tw", 1)
	} else {
		urlPath = strings.Replace(s3Path, "s3://kktv-prod-theater", "https://theater-kktv.cdn.hinet.net", 1)
	}
	return
}

func toTheaterHost(hostname string) (host string) {
	if strings.Contains(hostname, "test") {
		host = "test-theater.kktv.com.tw"
	} else {
		host = "theater.kktv.com.tw"
	}
	return host
}

func getSupportedQuality(i *ExtraRowMeta) []string {
	var supportedQuality []string
	var profile *StreamingProfile
	if i.Dash.URI != "" && i.Dash.Size != 0 {
		profile = &i.Dash
	} else if i.Hls.URI != "" && i.Hls.Size != 0 {
		profile = &i.Hls
	}

	if profile == nil {
		return supportedQuality
	}
	if profile.Sizes.Two40p != 0 {
		supportedQuality = append(supportedQuality, "240p")
	}
	if profile.Sizes.Three60p != 0 {
		supportedQuality = append(supportedQuality, "360p")
	}
	if profile.Sizes.Four80p != 0 {
		supportedQuality = append(supportedQuality, "480p")
	}
	if profile.Sizes.Seven20p != 0 {
		supportedQuality = append(supportedQuality, "720p")
	}
	if profile.Sizes.One080p != 0 {
		supportedQuality = append(supportedQuality, "1080p")
	}
	return supportedQuality
}

// SelectTargetFile decides targetFile by different device, purpose, and quality
func SelectTargetFile(device string, quality string, purpose string, platformStr string) (targetFile string) {
	// playback
	if purpose == "playback" {
		switch device {
		case "android":
			switch quality {
			case "playzone":
				targetFile = "lowest_bitrate_of_360p_or_p2"
			case "low":
				targetFile = "p2"
			case "medium":
				targetFile = "p3"
			case "high":
				// [BEGIN] workaround for https://kktv.atlassian.net/browse/KKTV-13818
				// 現 MOD 機器行為如下： （本身是一個只為了拿到對應 resolution profile 的 workaround）
				//   1. 若支援tunneled，則支援1080p，則 device parameter 帶入 `chromecast`
				//   2. 若不支援tunneled，則最高只支援720p)，則 device parameter 帶入 `android`
				// 因純用 device parameter 無法知道到底是 真android device 或是 MOD device, 但又需要讓真 android 用戶可以觀看 1080p，
				// 故只能再使用 headerPlatform 來判斷 真android device時，給予 1080p profile
				// TODO in the future, 應該直接使用 header platform 搭配 client 要求的 quality 來給予對應的 profile
				if platformStr == "android" {
					targetFile = "p5"
				} else {
					targetFile = "p4"
				}
				// [END]
			}
		case "ios":
			switch quality {
			case "playzone":
				targetFile = "lowest_bitrate_of_360p_or_p2"
			case "low":
				targetFile = "p2"
			case "medium":
				targetFile = "p3"
			case "high":
				targetFile = "p5"
			}
		case "web":
			switch quality {
			case "playzone":
				targetFile = "lowest_bitrate_of_360p_or_p2"
			case "low":
				targetFile = "p3"
			case "medium":
				targetFile = "p4"
			case "high":
				targetFile = "p5"
			}
		case "chromecast":
			switch quality {
			case "playzone":
				targetFile = "lowest_bitrate_of_360p_or_p2"
			case "low":
				targetFile = "p3"
			case "medium":
				targetFile = "p3"
			case "high":
				targetFile = "p5"
			}
		case "airplay":
			switch quality {
			case "playzone":
				targetFile = "lowest_bitrate_of_360p_or_p2"
			case "low":
				targetFile = "p3"
			case "medium":
				targetFile = "p3"
			case "high":
				targetFile = "p5"
			}
		}
	}

	// download
	if purpose == "download" {
		switch device {
		case "android":
			switch quality {
			case "medium":
				targetFile = "p3"
			case "high":
				targetFile = "p4"
			}
		case "ios":
			switch quality {
			case "medium":
				targetFile = "p3"
			case "high":
				targetFile = "p4"
			}
		}
	}
	return
}

// NewManifest get meta_extra and meta_episode table item to redis
// refer https://github.com/KKTV/kktv-db-redis-manifests/blob/develop/config/prod.py
// refer https://github.com/KKTV/kktv-s3-theater/blob/0626c7ee01ff28a46c2bee1bef09baa77e10b9b0/tests/test_main.py
func NewManifest(EpisodeIDs []string, targetFile string, subTitle bool) (mezzanines []Mezzanine, err error) {
	if len(EpisodeIDs) == 0 || len(EpisodeIDs[0]) != 14 {
		return
	}
	titleID := EpisodeIDs[0][0:8]

	if targetFile == "" {
		err = errors.New("no found")
		return
	}

	// prepare the data

	extras := []*ExtraRowMeta{}
	resps := [][]byte{}
	// extra = new(ExtraRowMeta)
	key := fmt.Sprintf(keyManifestFmt, titleID)
	pool := kkapp.App.RedisMeta.Slave()

	resps, _ = pool.Cmd("HMGET", key, EpisodeIDs).ListBytes()

	for idx, itemBytes := range resps {
		idStr := EpisodeIDs[idx]
		if len(itemBytes) > 0 {
			// had redis result
			item := new(ExtraRowMeta)
			err = json.Unmarshal(itemBytes, item)
			if err != nil {
				return nil, err
			}
			extras = append(extras, item)
		} else {
			// try databse
			item, _, err := Manifest2Redis(idStr)
			if err != nil {
				return nil, err
			}
			extras = append(extras, item)
		}
	}
	// compose the response
	for _, i := range extras {
		i.Parse()
		var item Mezzanine
		var dash, hls map[string]ExtraURI
		if len(i.SubtitleSlice) > 0 {
			subtitleMap := make(map[string]string)
			for k, v := range i.SubtitleMap {
				subtitleMap[k] = s3Path2CDN(v)
			}
			if _, ok := subtitleMap["zh-Hant"]; ok {
				item.DefaultSubtitle = "zh-Hant"
			} else {
				item.DefaultSubtitle = i.SubtitleSlice[0]
			}

			item.SubtitleURL = subtitleMap
		}

		// only at  purpose => playback  && quality => playzone
		if targetFile == "lowest_bitrate_of_360p_or_p2" {
			if subTitle {
				// with subtitle
				dash = i.DashUrisSub.Playback
				hls = i.HlsUrisSub.Playback
				if dash == nil || hls == nil {
					dash = i.DashUrisNosub.Playback
					hls = i.HlsUrisNosub.Playback
				}
			} else {
				// no subtitle
				dash = i.DashUrisNosub.Playback
				hls = i.HlsUrisNosub.Playback
			}

			// dash
			if one, ok := dash["p1"]; ok && one.Resolution == "360p" {
				item.Dash = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
			} else {
				one, ok := dash["p2"]
				if ok {
					item.Dash = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
				}
			}

			// hls
			if one, ok := hls["p1"]; ok && one.Resolution == "360p" {
				item.Hls = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
			} else {
				one, ok := hls["p2"]
				if ok {
					item.Hls = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
				}
			}
			if item.Dash.URL != "" && item.Hls.URL != "" {
				item.EpisodeID = i.EpisodeID
				mezzanines = append(mezzanines, item)
			}
			// next one
			continue
		}

		// purpose playback && quality (no quality == playzone)
		if purpose == "playback" {
			if subTitle {
				// with subtitle
				dash = i.DashUrisSub.Playback
				hls = i.HlsUrisSub.Playback
				if dash == nil || hls == nil {
					dash = i.DashUrisNosub.Playback
					hls = i.HlsUrisNosub.Playback
				}
			} else {
				// no subtitle
				dash = i.DashUrisNosub.Playback
				hls = i.HlsUrisNosub.Playback
			}
		} else if purpose == "download" {
			if subTitle {
				// with subtitle
				dash = i.DashUrisSub.Download
				hls = i.HlsUrisSub.Download
				if dash == nil || hls == nil {
					dash = i.DashUrisNosub.Download
					hls = i.HlsUrisNosub.Download
				}
			} else {
				// no subtitle
				dash = i.DashUrisNosub.Download
				hls = i.HlsUrisNosub.Download
			}
		}

		// dash
		if one, ok := dash[targetFile]; ok {
			item.Dash = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
		}

		// hls
		if one, ok := hls[targetFile]; ok {
			item.Hls = MezzanineFile{URL: s3Path2CDN(one.URI), Size: one.Size}
		}
		item.SupportedQuality = getSupportedQuality(i)

		if item.Dash.URL != "" && item.Hls.URL != "" {
			item.EpisodeID = i.EpisodeID
			// compose pre define thumbnail vtt path, avoid the trail episode
			if _, err := strconv.Atoi(i.EpisodeID); err == nil {
				// the EpisodeID not 00000338tr001 something like that, not a trailer
				hlsURL, err := url.Parse(item.Hls.URL)
				folderName := filepath.Dir(hlsURL.Path)
				// only kktv host images would have thumbnail
				if err == nil && strings.Contains(hlsURL.Host, "kktv") && strings.Contains(folderName, "_") {
					hashID := strings.Split(folderName, "_")[1]
					item.ThumbnailURL = fmt.Sprintf("%s://%s%s/%s/thumbnail.vtt", hlsURL.Scheme, toTheaterHost(hlsURL.Host), folderName, hashID)
					item.ThumbnailSmallURL = fmt.Sprintf("%s://%s%s/%s/thumbnailsmall.vtt", hlsURL.Scheme, toTheaterHost(hlsURL.Host), folderName, hashID)
				}
			}
			mezzanines = append(mezzanines, item)
		}
	}
	return
}
