package linetv

type PostEpisodeManifestRequest struct {
	UserID  string `json:"user_id" validate:"required"`
	Quality string `json:"quality" validate:"required,oneof=p0 p1 p2 p3 p4 p5"`
	Purpose string `json:"purpose"`
}

// validateQuality checks if the quality parameter is valid (p0-p5)
func validateQuality(quality string) bool {
	validQualities := map[string]bool{
		"p0": true,
		"p1": true,
		"p2": true,
		"p3": true,
		"p4": true,
		"p5": true,
	}
	return validQualities[quality]
}
