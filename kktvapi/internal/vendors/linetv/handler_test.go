package linetv

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/manifest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type LineTVHandlerSuite struct {
	suite.Suite
	ctrl                     *gomock.Controller
	mockManifestLegacyHelper *manifest.MockLegacyHelper
	mockPlaybackLegacyHelper *playback.MockLegacyHelper
	mockClock                *clock.MockClock

	handler Handler
	app     *bone.Mux
}

func TestLineTVHandlerSuite(t *testing.T) {
	suite.Run(t, new(LineTVHandlerSuite))
}

func (suite *LineTVHandlerSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockManifestLegacyHelper = manifest.NewMockLegacyHelper(suite.ctrl)
	suite.mockPlaybackLegacyHelper = playback.NewMockLegacyHelper(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)

	suite.handler = Handler{
		manifestLegacyHelper: suite.mockManifestLegacyHelper,
		playbackLegacyHelper: suite.mockPlaybackLegacyHelper,
		clock:                suite.mockClock,
	}

	suite.app = bone.New()
	suite.app.Post("/vendors/linetv/episodes/:episodeID/manifest", http.HandlerFunc(suite.handler.RequireStreamingManifest))
}

func (suite *LineTVHandlerSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *LineTVHandlerSuite) TestPostEpisodeManifest() {
	testcases := []struct {
		name           string
		episodeID      string
		deviceID       string
		requestBody    PostEpisodeManifestRequest
		given          func()
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name:      "WHEN valid p4 quality request THEN return manifest and playback token",
			episodeID: "01000749010001",
			deviceID:  "test-device-id",
			requestBody: PostEpisodeManifestRequest{
				UserID:  "test-user-id",
				Quality: "p4",
				Purpose: "playback",
			},
			given: func() {
				mezzanines := []manifest.Mezzanine{
					{
						Dash: dbmeta.MezzanineFile{URL: "https://example.com/dash.mpd"},
						Hls:  dbmeta.MezzanineFile{URL: "https://example.com/hls.m3u8"},
					},
				}
				suite.mockManifestLegacyHelper.EXPECT().
					GetManifestsByTargetFile([]string{"01000749010001"}, "p4", true).
					Return(mezzanines, nil)

				suite.mockPlaybackLegacyHelper.EXPECT().
					GetToken("test-user-id", "test-device-id", playback.PlaybackRequest{
						TitleID:   "01000749",
						EpisodeID: "01000749010001",
						Medium:    "SVOD",
						Purpose:   "playback",
					}).
					Return(&playback.Playback{
						PlaybackToken: "test-playback-token",
					}, nil)
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var response struct {
					Data PostEpisodeManifestResponse `json:"data"`
				}
				suite.NoError(json.Unmarshal(body, &response))
				suite.Equal("https://example.com/dash.mpd", response.Data.StreamingAssets.DashURL)
				suite.Equal("https://example.com/hls.m3u8", response.Data.StreamingAssets.HlsURL)
				suite.Equal("test-playback-token", response.Data.PlaybackToken)
				suite.NotEmpty(response.Data.LicenseURL)
			},
		},
		{
			name:      "WHEN invalid quality p6 THEN return 400 error",
			episodeID: "01000749010001",
			deviceID:  "test-device-id",
			requestBody: PostEpisodeManifestRequest{
				UserID:  "test-user-id",
				Quality: "p6",
				Purpose: "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN missing device ID THEN return 400 error",
			episodeID: "01000749010001",
			deviceID:  "",
			requestBody: PostEpisodeManifestRequest{
				UserID:  "test-user-id",
				Quality: "p4",
				Purpose: "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN invalid episode ID THEN return 400 error",
			episodeID: "invalid",
			deviceID:  "test-device-id",
			requestBody: PostEpisodeManifestRequest{
				UserID:  "test-user-id",
				Quality: "p4",
				Purpose: "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN episode not found THEN return 404 error",
			episodeID: "01000749010001",
			deviceID:  "test-device-id",
			requestBody: PostEpisodeManifestRequest{
				UserID:  "test-user-id",
				Quality: "p4",
				Purpose: "playback",
			},
			given: func() {
				suite.mockManifestLegacyHelper.EXPECT().
					GetManifestsByTargetFile([]string{"01000749010001"}, "p4", true).
					Return([]manifest.Mezzanine{}, nil)
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			requestBody, _ := json.Marshal(tc.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/vendors/linetv/episodes/"+tc.episodeID+"/manifest", bytes.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")
			if tc.deviceID != "" {
				req.Header.Set(httpreq.HeaderDeviceID, tc.deviceID)
			}

			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}
