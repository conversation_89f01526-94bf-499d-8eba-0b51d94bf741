package linetv

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/manifest"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	manifestLegacyHelper  manifest.LegacyHelper
	playbackLegacyHelper  playback.LegacyHelper
	clock                 clock.Clock
}

func NewHandler() *Handler {
	metaCacheReader := cache.New(container.CachePoolMeta().Slave())
	return &Handler{
		manifestLegacyHelper: manifest.NewLegacyHelper(
			clock.New(),
			config.TheaterCDNSignKey,
			metaCacheReader,
		),
		playbackLegacyHelper: playback.NewLegacyHelper(),
		clock:                clock.New(),
	}
}

func (h *Handler) PostEpisodeManifest(w http.ResponseWriter, r *http.Request) {
	episodeID := bone.GetValue(r, "episodeID")
	if episodeID == "" || len(episodeID) != 14 {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	deviceID := r.Header.Get(httpreq.HeaderDeviceID)
	if deviceID == "" {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	var req PostEpisodeManifestRequest
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		plog.Error("linetvHandler: PostEpisodeManifest: failed to bind payload").Err(err).Send()
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	if !validateQuality(req.Quality) {
		render.JSONBadRequest(w, ErrInvalidQuality)
		return
	}

	if req.Purpose == "" {
		req.Purpose = "playback"
	}

	titleID := episodeID[:8]
	targetFile := req.Quality

	manifests, err := h.manifestLegacyHelper.GetManifestsByTargetFile([]string{episodeID}, targetFile, true)
	if err != nil {
		plog.Error("linetvHandler: PostEpisodeManifest: failed to get manifests").
			Err(err).
			Str("episode_id", episodeID).
			Str("target_file", targetFile).
			Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	}

	if len(manifests) == 0 {
		render.JSONNotFound(w, ErrEpisodeNotFound)
		return
	}

	manifest := manifests[0]

	playbackRequest := playback.PlaybackRequest{
		TitleID:   titleID,
		EpisodeID: episodeID,
		Medium:    "SVOD",
		Purpose:   req.Purpose,
	}

	playbackToken, err := h.playbackLegacyHelper.GetToken(req.UserID, deviceID, playbackRequest)
	if err != nil {
		plog.Error("linetvHandler: PostEpisodeManifest: failed to get playback token").
			Err(err).
			Str("user_id", req.UserID).
			Str("device_id", deviceID).
			Str("episode_id", episodeID).
			Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	}

	response := PostEpisodeManifestResponse{
		StreamingAssets: StreamingAsset{
			DashURL: manifest.Dash.URL,
			HlsURL:  manifest.Hls.URL,
		},
		LicenseURL: map[string]string{
			"playready": config.KKSBVLicenseUrl,
			"widevine":  config.KKSBVLicenseUrl,
			"fairplay":  config.LicenseUrlFairplay,
		},
		PlaybackToken: playbackToken.PlaybackToken,
	}

	resp := rest.Ok()
	resp.Data = response
	render.JSON(w, http.StatusOK, resp)
}
