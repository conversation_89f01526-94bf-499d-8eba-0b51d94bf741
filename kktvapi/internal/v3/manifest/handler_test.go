package manifest

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

// test suite for handler
type HandlerTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	app                 *bone.Mux
	handler             *Handler
	mockLegacyHelper    *MocklegacyHelper
	mockMetaCacheReader *cache.MockCacher
	mockFeatureService  *feature.MockService
	mockEpisodeRepo     *meta.MockEpisodeRepository
	mockPermissionSrv   *permission.MockService
	mockTitleRepo       *meta.MockTitleRepository
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupSuite() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)
	suite.mockMetaCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockFeatureService = feature.NewMockService(suite.ctrl)
	suite.mockEpisodeRepo = meta.NewMockEpisodeRepository(suite.ctrl)
	suite.mockPermissionSrv = permission.NewMockService(suite.ctrl)
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)

	suite.handler = &Handler{
		legacy:            suite.mockLegacyHelper,
		metaCacheReader:   suite.mockMetaCacheReader,
		featureService:    suite.mockFeatureService,
		episodeRepo:       suite.mockEpisodeRepo,
		permissionService: suite.mockPermissionSrv,
		titleRepo:         suite.mockTitleRepo,
	}

	suite.app = bone.New()
	suite.app.GetFunc("/v3/titles/:titleid/episodes/:episodeids/mezzanine/manifests", suite.handler.GetManifest)

	config.LicenseUrlFairplay = "https://kktv.me/license"
	config.LicenseUrlWidevine = "https://kktv.me/license"
	config.LicenseUrlPlayready = "https://kktv.me/license"
	config.KKSBVTenantID = "kktv-vip"
	config.KKSBVLicenseUrl = "https://blendvision.com/license"
}

func (suite *HandlerTestSuite) SetupTest() {

}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()

}

type resp struct {
	Status rest.Status   `json:"status"`
	Data   manifestsResp `json:"data"`
}

func (suite *HandlerTestSuite) TestGetManifest() {
	const (
		defaultTitleID = "01060429"
		defaultEpisode = "01060429010001"
		extraTitleID   = "01060429extra"
		extraEpisode   = "01060429ot0001"
	)
	var (
		defaultParams    = "quality=high&device=ios&subtitles=0&purpose=playback&medium=SVOD"
		defaultMezzanies = []Mezzanine{
			{
				EpisodeID:       "01060429010001",
				DefaultSubtitle: "zh-Hant",
				SubtitleURL: map[string]string{
					"zh-Hant": "https://theater-kktv.cdn.hinet.net/52/01060429010001/zh-Hant.vtt",
				},
				ThumbnailURL:      "https://theater.kktv.com.tw/52/01060429010001/thumbnail.vtt",
				ThumbnailSmallURL: "https://theater.kktv.com.tw/52/01060429010001/thumbnailsmall.vtt",
				Dash: dbmeta.MezzanineFile{
					URL:  "https://theater-kktv.cdn.hinet.net/52/01060429010001_dash.playback_p3.mpd",
					Size: 7707284904.4191,
				},
				Hls: dbmeta.MezzanineFile{
					URL:  "https://theater-kktv.cdn.hinet.net/52/01060429010001/1651233722aaaae3c074_hls.playback_p3.m3u8",
					Size: 8225496278.999999,
				},
			},
		}
	)
	var (
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
			},
			)
			return req.WithContext(ctx)
		}
		assertManifest = func(r *resp) {
			suite.Equal(defaultMezzanies[0].EpisodeID, r.Data.Episodes[0].EpisodeID)
			suite.Equal(defaultMezzanies[0].DefaultSubtitle, r.Data.Episodes[0].DefaultSubtitle)
			suite.Equal(defaultMezzanies[0].SubtitleURL, r.Data.Episodes[0].SubtitleURL)
			suite.Equal(defaultMezzanies[0].ThumbnailURL, r.Data.Episodes[0].ThumbnailURL)
			suite.Equal(defaultMezzanies[0].ThumbnailSmallURL, r.Data.Episodes[0].ThumbnailSmallURL)
			suite.Equal(defaultMezzanies[0].Dash.URL, r.Data.Episodes[0].Dash.URL)
			suite.Equal(defaultMezzanies[0].Dash.Size, r.Data.Episodes[0].Dash.Size)
			suite.Equal(defaultMezzanies[0].Hls.URL, r.Data.Episodes[0].Hls.URL)
			suite.Equal(defaultMezzanies[0].Hls.Size, r.Data.Episodes[0].Hls.Size)
		}
		assertLicenseURLs = func(url string, r *resp) {
			suite.Equal(url, r.Data.LicenseURL["fairplay"])
			suite.Equal(url, r.Data.LicenseURL["playready"])
			suite.Equal(url, r.Data.LicenseURL["widevine"])
		}
	)

	testcases := []struct {
		name        string
		titleID     string
		epID        string
		queryParams string
		modifyReq   func(req *http.Request) *http.Request
		given       func()
		thenAssert  func(code int, body []byte)
	}{
		{
			name:    "Got manifest of AVOD and BV drm license urls WHEN login user is premium AND BV_DRM is enabled",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: defaultParams,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				req.Header.Set(httpreq.HeaderDeviceID, "josie-deviceid")
				req.Header.Set(httpreq.HeaderPlatform, "Android")
				return req
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{
					Available: true, IsAvod: false, FreeTrial: true,
					EpisodeID: "01060429010001",
				}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				tds := []*legacymeta.TitleDetail{
					{LegacyTitleDetail: &model.TitleDetail{ID: "01060429"}},
				}
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)

				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.MembershipPremiumOnly)).Return(nil)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, true)).Return(nil)
				suite.mockGrantPermissionAdvancedPlayerFunction(dbuser.MembershipPremiumOnly)
				suite.mockLegacyHelper.EXPECT().GetManifests([]string{"01060429010001"}, "ios", "high", "playback", "android", false).Return(defaultMezzanies, nil)
				suite.mockFeatureService.EXPECT().HasFlag(feature.FlagUseBvDRM, gomock.Any()).Return(true, nil)
				suite.mockMetaCacheHGetGeneralConfigLicenseReqHeader(map[string]string{
					"X-Device-Id": "", "X-KK-Tenant-Id": "",
				})
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				var r resp
				suite.NoError(json.Unmarshal(body, &r))

				assertLicenseURLs("https://blendvision.com/license", &r)
				suite.ElementsMatch([]headerItem{
					{Key: "X-Device-Id", Value: "josie-deviceid"},
					{Key: "X-KK-Tenant-Id", Value: "kktv-vip"},
				}, r.Data.LicenseHeaders)
				assertManifest(&r)
			},
		},
		{
			name:    "WHEN expired user access non-AVOD episode, got FORBIDDEN",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: defaultParams,
			modifyReq: func(req *http.Request) *http.Request {
				return setRequestLogin(req, dbuser.MembershipExpired)
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{
					Available: true, IsAvod: false,
				}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				tds := []*legacymeta.TitleDetail{
					{
						LegacyTitleDetail: &model.TitleDetail{ID: "01060429"},
					},
				}
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)

				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.Membership{dbuser.RoleModel{Role: dbuser.MemberRoleExpired}})).Return(kktverror.ErrResourceAccessDenied)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, false)).Return(kktverror.ErrResourceAccessDenied)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusForbidden, code)
			},
		},
		{
			name:    "WHEN freeTrial user access non-freetrial episode, got FORBIDDEN",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: defaultParams,
			modifyReq: func(req *http.Request) *http.Request {
				return setRequestLogin(req, dbuser.MembershipFreeTrial)
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{
					Available: true, IsAvod: false, FreeTrial: false,
				}
				tds := []*legacymeta.TitleDetail{
					{
						&model.TitleDetail{ID: "01060429"},
					},
				}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.MembershipFreeTrial)).Return(kktverror.ErrResourceAccessDenied)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, false)).Return(kktverror.ErrResourceAccessDenied)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusForbidden, code)
			},
		},
		{
			name:    "WHEN episode is not available, got FORBIDDEN",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: defaultParams,
			modifyReq: func(req *http.Request) *http.Request {
				return setRequestLogin(req, dbuser.MembershipPremiumOnly)
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{
					Available: false, IsAvod: false,
				}
				tds := []*legacymeta.TitleDetail{
					{LegacyTitleDetail: &model.TitleDetail{ID: "01060429"}},
				}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.MembershipPremiumOnly)).Return(kktverror.ErrResourceAccessDenied)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, false)).Return(kktverror.ErrResourceAccessDenied)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusForbidden, code)
			},
		},
		{
			name:    "Got manifest and kktv drm license urls WHEN Guest access but BV_DRM is disabled",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: defaultParams,
			modifyReq: func(req *http.Request) *http.Request {
				r := setRequestLogin(req, dbuser.NonMember)
				r.Header.Set(httpreq.HeaderPlatform, "Android")
				return r
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{Available: true, IsAvod: true}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				tds := []*legacymeta.TitleDetail{
					{
						LegacyTitleDetail: &model.TitleDetail{ID: "01060429"},
					},
				}
				suite.mockFeatureService.EXPECT().HasFlag(feature.FlagUseBvDRM, gomock.Any()).Return(false, nil)
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)

				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.Membership{})).Return(kktverror.ErrResourceAccessDenied)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, false)).Return(nil)
				suite.mockLegacyHelper.EXPECT().GetManifests([]string{"01060429010001"}, "ios", "medium", "playback", "android", false).Return(defaultMezzanies, nil)
				suite.mockNotGrantPermissionAdvancedPlayerFunction(dbuser.NonMember)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				var r resp
				suite.NoError(json.Unmarshal(body, &r))
				assertManifest(&r)
				suite.Nil(r.Data.LicenseHeaders)
			},
		},
		{
			name:    "no input query params",
			titleID: defaultTitleID, epID: defaultEpisode,
			queryParams: "",
			modifyReq: func(req *http.Request) *http.Request {
				req.Header.Set(httpreq.HeaderPlatform, "Android")
				return setRequestLogin(req, dbuser.MembershipPremiumOnly)
			},
			given: func() {
				ep := dbmeta.EpisodeMeta{Available: true, IsAvod: false}
				suite.mockLegacyGetEpByID("01060429010001", ep)
				tds := []*legacymeta.TitleDetail{
					{LegacyTitleDetail: &model.TitleDetail{ID: "01060429"}},
				}
				suite.mockTitleRepoListedTitleDetails([]string{"01060429"}, tds)

				membership := dbuser.Membership{dbuser.RoleModel{Role: dbuser.MemberRolePremium}}
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], membership)).Return(kktverror.ErrResourceAccessDenied)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestPlayEpisode(&ep, false)).Return(nil)
				suite.mockNotGrantPermissionAdvancedPlayerFunction(membership)
				suite.mockLegacyHelper.EXPECT().GetManifests(
					[]string{"01060429010001"},
					"",         // device
					"medium",   // quality
					"playback", // purpose
					"android",  // platform
					true,       // subtitles
				).Return(defaultMezzanies, nil)

				suite.mockFeatureService.EXPECT().HasFlag(feature.FlagUseBvDRM, gomock.Any()).Return(false, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				var r resp
				suite.NoError(json.Unmarshal(body, &r))
				assertManifest(&r)
				assertLicenseURLs("https://kktv.me/license", &r)
				suite.Nil(r.Data.LicenseHeaders)
			},
		},
		{
			name:    "query extra episode info",
			titleID: extraTitleID, epID: extraEpisode,
			queryParams: "",
			modifyReq: func(req *http.Request) *http.Request {
				return setRequestLogin(req, dbuser.MembershipPremiumOnly)
			},
			given: func() {
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessExtraTitle(dbuser.MembershipPremiumOnly)).Return(nil)
				suite.mockLegacyHelper.EXPECT().GetManifests([]string{extraEpisode}, "", "", "playback", "", true).Return(defaultMezzanies, nil)

				suite.mockFeatureService.EXPECT().HasFlag(feature.FlagUseBvDRM, gomock.Any()).Return(false, nil)
				suite.mockGrantPermissionAdvancedPlayerFunction(dbuser.MembershipPremiumOnly)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				var r resp
				suite.NoError(json.Unmarshal(body, &r))
				assertManifest(&r)
				assertLicenseURLs("https://kktv.me/license", &r)
				suite.Nil(r.Data.LicenseHeaders)
			},
		},
		{
			name:    "query extra episode but not found",
			titleID: extraTitleID, epID: extraEpisode,
			queryParams: "",
			modifyReq: func(req *http.Request) *http.Request {
				return setRequestLogin(req, dbuser.MembershipPremiumOnly)
			},
			given: func() {
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessExtraTitle(dbuser.Membership{dbuser.RoleModel{Role: dbuser.MemberRolePremium}})).Return(nil)
				suite.mockLegacyHelper.EXPECT().GetManifests([]string{extraEpisode}, "", "", "playback", "", true).Return(nil, nil)
				suite.mockGrantPermissionAdvancedPlayerFunction(dbuser.MembershipPremiumOnly)

			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusNotFound, code)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet,
				fmt.Sprintf("/v3/titles/%s/episodes/%s/mezzanine/manifests?%s", tc.titleID, tc.epID, tc.queryParams), nil)
			req = tc.modifyReq(req)

			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}

}

func (suite *HandlerTestSuite) mockNotGrantPermissionAdvancedPlayerFunction(membership dbuser.Membership) *gomock.Call {
	return suite.mockPermissionSrv.EXPECT().HasAdvancedPlayerFunction(membership).Return(false, nil)
}
func (suite *HandlerTestSuite) mockGrantPermissionAdvancedPlayerFunction(membership dbuser.Membership) *gomock.Call {
	return suite.mockPermissionSrv.EXPECT().HasAdvancedPlayerFunction(membership).Return(true, nil)
}

func (suite *HandlerTestSuite) mockTitleRepoListedTitleDetails(titleIDs []string, tds []*legacymeta.TitleDetail) *gomock.Call {
	return suite.mockTitleRepo.EXPECT().ListViewableTitleDetailWithoutSeries(titleIDs, true).Return(tds, nil)
}

func (suite *HandlerTestSuite) mockLegacyGetEpByID(epID string, ep dbmeta.EpisodeMeta) {
	suite.mockLegacyHelper.EXPECT().GetEpisodeByID(epID).Return(&ep, nil)
}

func (suite *HandlerTestSuite) mockMetaCacheHGetGeneralConfigLicenseReqHeader(headers map[string]string) *gomock.Call {
	return suite.mockMetaCacheReader.EXPECT().HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.LicenseRequestHeaders, gomock.Any()).
		DoAndReturn(func(_ string, _ string, v interface{}) error {
			*(v.(*map[string]string)) = headers
			return nil
		})
}
