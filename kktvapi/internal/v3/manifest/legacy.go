//go:generate mockgen -source legacy.go -destination legacy_mock.go -package manifest
package manifest

import (
	"database/sql"
	"errors"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
)

type mezzanine dbmeta.Mezzanine

func (m *mezzanine) withURLSignature(secret string, now time.Time) {
	if len(m.SubtitleURL) > 0 {
		for lang, u := range m.SubtitleURL {
			finalURL, err := withSignature(u, secret, now)
			if err != nil {
				continue
			}
			m.SubtitleURL[lang] = finalURL
		}
	}
	if m.Dash.URL != "" {
		finalURL, err := withSignature(m.Dash.URL, secret, now)
		if err == nil {
			m.Dash.URL = finalURL
		}
	}
	if m.Hls.URL != "" {
		finalURL, err := withSignature(m.Hls.URL, secret, now)
		if err == nil {
			m.Hls.URL = finalURL
		}
	}
}

type legacy struct {
	clock              clock.Clock
	cdnSignatureSecret string
	metaCacheReader    cache.Cacher
}

var _ legacyHelper = (*legacy)(nil)

func newLegacy(clock clock.Clock, cdnSignatureSecret string, metaCacheReader cache.Cacher) *legacy {
	return &legacy{
		clock:              clock,
		cdnSignatureSecret: cdnSignatureSecret,
		metaCacheReader:    metaCacheReader,
	}
}

// interface for legacyHelper
type legacyHelper interface {
	GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]mezzanine, error)
	GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error)
}

func (h *legacy) GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]mezzanine, error) {
	targetFile := dbmeta.SelectTargetFile(device, quality, purpose, platform)
	legacyMezzanine, err := dbmeta.NewManifest(epIDs, targetFile, withSubtitle)
	// the `dbmeta.NewManifest()` will query in redis then query in db, so finally got`sql.ErrNoRows` if no data found
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, nil
	}
	mezzanines := make([]mezzanine, len(legacyMezzanine))
	for i, m := range legacyMezzanine {
		mezzanines[i] = mezzanine(m)
		mezzanines[i].withURLSignature(h.cdnSignatureSecret, h.clock.Now())
	}
	return mezzanines, err
}

func (h *legacy) GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error) {
	titleID, seriesID := epID[:8], epID[:10]
	t := new(model.TitleDetail)
	// FIXME: https://kktv.atlassian.net/browse/KKTV-11984
	// This is a workaround to get the correct `PlayZone` meta of Episode,
	// due to the `PlayZone` property stored in DB is not finally correct (Not processed by the `PlayZoneByEpisodes` logic
	if err := h.metaCacheReader.HGet(key.TitleDetail(titleID), "whole", t); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	for _, ser := range t.Series {
		if ser.ID != seriesID {
			continue
		}
		for _, e := range ser.Episodes {
			if e.ID == epID {
				return e, nil
			}
		}
	}
	return nil, nil
}
