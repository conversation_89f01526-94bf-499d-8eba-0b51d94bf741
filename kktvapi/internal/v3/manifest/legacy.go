//go:generate mockgen -source legacy.go -destination legacy_mock.go -package manifest
package manifest

import (
	"database/sql"
	"errors"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
)

type Mezzanine dbmeta.Mezzanine

func (m *Mezzanine) withURLSignature(secret string, now time.Time) {
	if len(m.SubtitleURL) > 0 {
		for lang, u := range m.SubtitleURL {
			finalURL, err := withSignature(u, secret, now)
			if err != nil {
				continue
			}
			m.SubtitleURL[lang] = finalURL
		}
	}
	if m.Dash.URL != "" {
		finalURL, err := withSignature(m.Dash.URL, secret, now)
		if err == nil {
			m.Dash.URL = finalURL
		}
	}
	if m.Hls.URL != "" {
		finalURL, err := withSignature(m.Hls.URL, secret, now)
		if err == nil {
			m.Hls.URL = finalURL
		}
	}
}

type legacy struct {
	clock              clock.Clock
	cdnSignatureSecret string
	metaCacheReader    cache.Cacher
}

type mezzanine Mezzanine

var _ legacyHelper = (*legacy)(nil)
var _ LegacyHelper = (*legacy)(nil)

func newLegacy(clock clock.Clock, cdnSignatureSecret string, metaCacheReader cache.Cacher) *legacy {
	return &legacy{
		clock:              clock,
		cdnSignatureSecret: cdnSignatureSecret,
		metaCacheReader:    metaCacheReader,
	}
}

// NewLegacyHelper creates a new public LegacyHelper instance
func NewLegacyHelper(clock clock.Clock, cdnSignatureSecret string, metaCacheReader cache.Cacher) LegacyHelper {
	return &legacy{
		clock:              clock,
		cdnSignatureSecret: cdnSignatureSecret,
		metaCacheReader:    metaCacheReader,
	}
}

// interface for legacyHelper
type legacyHelper interface {
	GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]mezzanine, error)
	GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error)
}

// LegacyHelper is the public interface for manifest legacy operations
type LegacyHelper interface {
	GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]mezzanine, error)
	GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error)
	GetManifestsByTargetFile(epIDs []string, targetFile string, withSubtitle bool) ([]Mezzanine, error)
}

func (h *legacy) GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]mezzanine, error) {
	targetFile := dbmeta.SelectTargetFile(device, quality, purpose, platform)
	dbMezzanines, err := h.GetManifestsByTargetFile(epIDs, targetFile, withSubtitle)
	if err != nil {
		return nil, err
	}
	mezzanines := make([]mezzanine, len(dbMezzanines))
	for i, m := range dbMezzanines {
		mezzanines[i] = mezzanine(m)
	}
	return mezzanines, nil
}

func (h *legacy) GetManifestsByTargetFile(epIDs []string, targetFile string, withSubtitle bool) ([]Mezzanine, error) {
	legacyMezzanine, err := dbmeta.NewManifest(epIDs, targetFile, "playback", withSubtitle)
	// the `dbmeta.NewManifest()` will query in redis then query in db, so finally got`sql.ErrNoRows` if no data found
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, nil
	}
	mezzanines := make([]Mezzanine, len(legacyMezzanine))
	for i, m := range legacyMezzanine {
		mezzanines[i] = Mezzanine(m)
		mezzanines[i].withURLSignature(h.cdnSignatureSecret, h.clock.Now())
	}
	return mezzanines, err
}

func (h *legacy) GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error) {
	titleID, seriesID := epID[:8], epID[:10]
	t := new(model.TitleDetail)
	// FIXME: https://kktv.atlassian.net/browse/KKTV-11984
	// This is a workaround to get the correct `PlayZone` meta of Episode,
	// due to the `PlayZone` property stored in DB is not finally correct (Not processed by the `PlayZoneByEpisodes` logic
	if err := h.metaCacheReader.HGet(key.TitleDetail(titleID), "whole", t); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	for _, ser := range t.Series {
		if ser.ID != seriesID {
			continue
		}
		for _, e := range ser.Episodes {
			if e.ID == epID {
				return e, nil
			}
		}
	}
	return nil, nil
}
