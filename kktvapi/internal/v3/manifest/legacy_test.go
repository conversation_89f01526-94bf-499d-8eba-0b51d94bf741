package manifest

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/stretchr/testify/assert"
)

func TestMezzanine_withURLSignature(t *testing.T) {
	var (
		now = time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC)
	)

	tests := []struct {
		name              string
		inputMezzanine    Mezzanine
		expectSubtitleURL map[string]string
		expectDashURL     string
		expectHlsURL      string
	}{
		{
			name: "test withURLSignature",
			inputMezzanine: Mezzanine{
				SubtitleURL: map[string]string{
					"zh-Hant": "https://cdn.kktv.me/zh-Hant/zh-Hant.vtt",
					"zh-<PERSON>": "https://cdn.kktv.me/zh-Hans/zh-Hans.vtt",
				},
				Dash: dbmeta.MezzanineFile{
					URL:  "https://cdn.kktv.me/dash.mpd",
					Size: 123,
				},
				Hls: dbmeta.MezzanineFile{
					URL:  "https://cdn.kktv.me/hls.m3u8",
					Size: 456,
				},
				ThumbnailURL: "https://cdn.kktv.me/thumbnail.jpg",
			},
			expectSubtitleURL: map[string]string{
				"zh-Hant": "https://cdn.kktv.me/zh-Hant/zh-Hant.vtt?e=1588204800&t=wJgLhxcEV4q7Mk-D22FXDg",
				"zh-Hans": "https://cdn.kktv.me/zh-Hans/zh-Hans.vtt?e=1588204800&t=ZxPhCxzJGQorigyWCe79jA",
			},
			expectDashURL: "https://cdn.kktv.me/dash.mpd?e=1588204800&t=fs-8VfHwvxFEwC3WKLtyZg",
			expectHlsURL:  "https://cdn.kktv.me/hls.m3u8?e=1588204800&t=PwBph7PI_FUgrLkeifhZmQ",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.inputMezzanine.withURLSignature("kktv-test", now)
			assert.Equal(t, tt.expectSubtitleURL, tt.inputMezzanine.SubtitleURL)
			assert.Equal(t, tt.expectDashURL, tt.inputMezzanine.Dash.URL)
			assert.Equal(t, tt.expectHlsURL, tt.inputMezzanine.Hls.URL)
		})
	}
}
