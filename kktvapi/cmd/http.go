package cmd

import (
	"embed"
	"fmt"
	"io/fs"
	"log"
	"net"
	"net/http"
	"runtime"

	"github.com/KKTV/kktv-api-v3/consoleapi"
	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkbilling"
	"github.com/KKTV/kktv-api-v3/kkapp/kkconsole"
	"github.com/KKTV/kktv-api-v3/kkapp/kkdrm"
	"github.com/KKTV/kktv-api-v3/kkapp/kkhandler"
	"github.com/KKTV/kktv-api-v3/kkapp/kkhandler/kkauth"
	"github.com/KKTV/kktv-api-v3/kkapp/kkmiddleware"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment/billing"
	"github.com/KKTV/kktv-api-v3/kkapp/kkredeem"
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/youtube"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/docs"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/apidoc"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/ematic"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/image"
	pmiddleware "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	mwappauth "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/appauth"
	mwauditing "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/recoverer"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/tracing"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	v0Auth "github.com/KKTV/kktv-api-v3/kktvapi/internal/v0/auth"
	v3Ads "github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/ads"
	v3Manifest "github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/manifest"
	v3Redeem "github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/redeem"
	v3Titlelist "github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/titlelist"
	v4 "github.com/KKTV/kktv-api-v3/kktvapi/internal/v4"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/vendors"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/broadcasting"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/order"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/kktvapi/web"
	pkgauth "github.com/KKTV/kktv-api-v3/pkg/auth"
	pkgbilling "github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/events"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/NYTimes/gziphandler"
	"github.com/go-zoo/bone"
	"github.com/golang-jwt/jwt"
	"github.com/gorilla/sessions"
	"github.com/justinas/alice"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"
	"github.com/rs/cors"
)

const (
	titleListExpireSoonRegex = `^expiredsoon(-\S+)?$`
)

func NewHttpServer() http.Handler {
	initialize()

	var (
		gplusLoginCallBack = ""
	)

	log.Println("Lambda kktv-api-v3 init")

	var common, authed alice.Chain
	var guestAuthed alice.Chain
	var commongeo, console, consoleUser, consoleProduct, consoleAdmin, consoleContent, consoleFinance, consoleRedeem alice.Chain
	var authedgeo alice.Chain
	var identified, basicAuth, jwtAuth alice.Chain
	var thirdPartyIPFilter alice.Chain
	var drmServerIPFilter alice.Chain
	var auditor = mwauditing.NewAuditor(container.AuditingRepo())
	var authorizator = mwappauth.NewAuth(appauth.NewRepository())

	mux := bone.New()

	// goth for google login
	store := sessions.NewCookieStore([]byte("kktv得第一"))
	store.Options.MaxAge = 86400 // 24hrs
	gothic.Store = store

	// middleware for general
	common = alice.New(
		recoverer.Recover,
		cors.New(cors.Options{
			AllowedHeaders: []string{
				"Content-Type", "Authorization", httpreq.HeaderDeviceID,
				httpreq.HeaderPlatform, httpreq.HeaderAppVersion, httpreq.HeaderCarrier,
				httpreq.HeaderOS, httpreq.HeaderOSVersion,
				httpreq.HeaderDeviceBrand, httpreq.HeaderDeviceManuFact, httpreq.HeaderDeviceModel,
			},
			AllowedMethods:   []string{"GET", "OPTIONS", "POST", "PUT", "DELETE", "PATCH"},
			MaxAge:           86400,
			AllowCredentials: true}).Handler,
	)
	commonWithoutLogger := common
	common = common.Append(pmiddleware.RequestLogger())
	// middleware for signed in user client
	authedWithoutLogger := commonWithoutLogger.Append(auth.JWTAuth, tracing.WithScope)
	authed = common.Append(auth.JWTAuth, tracing.WithScope)
	memberAuthed := common.Append(auth.MemberAuth, tracing.WithScope)
	// middleware for both signed or not signed
	guestAuthed = common.Append(auth.GuestAuth, tracing.WithScope)

	common = common.Append(tracing.WithScope)

	identified = guestAuthed.Append(pmiddleware.Identifier())
	basicAuth = common.Append(authorizator.BasicAuth)
	jwtAuth = common.Append(authorizator.JWTAuth)

	// middleware for signed in KKTV staff
	console = common.Append(kkmiddleware.JWTConsoleAuth("kktv得第一", "HS256", "user"))
	consoleUser = console.Append(kkmiddleware.RequireRole("user", "user"))
	consoleProduct = console.Append(kkmiddleware.RequireRole("user", "product"))
	consoleAdmin = console.Append(kkmiddleware.RequireRole("user", "admin"))
	consoleContent = console.Append(kkmiddleware.RequireRole("user", "content"))
	consoleFinance = console.Append(kkmiddleware.RequireRole("user", "finance"))
	consoleRedeem = console.Append(kkmiddleware.RequireRole("user", "redeem"))

	commongeo = common.Append(
		kkmiddleware.ServiceStatus(),
		kkmiddleware.RoamBlocker(kkapp.App.AllowedCountries, kkapp.App.IPWhiteList),
	)

	authedgeo = authed.Append(
		kkmiddleware.ServiceStatus(),
		kkmiddleware.RoamBlocker(kkapp.App.AllowedCountries, kkapp.App.IPWhiteList),
	)

	hideLustMW := pmiddleware.NewHideLustContentMiddleware(cache.New(container.CachePoolMeta().Slave()))
	hideLust := alice.New(
		kkmiddleware.ServiceStatus(),
		hideLustMW.Handle,
	)

	thirdPartyIPFilter = common.Append(
		kkmiddleware.ThirdPartyIPFilter(kkapp.App.ThirdPartyIPList),
	)

	cidrs := []*net.IPNet{}
	internalNetworks := []string{
		"10.0.0.0/8",        // aws internal
		"::1/128",           // localhost
		"*************/32",  // prod-jump
		"**************/32", // prod-nat-1a
		"***********/32",    // prod-nat-1c
		"*************/32",  // test-jump
		"*************/32",  // test-nat-1a
		"*************/32",  // test-nat-1c

		//[BEGIN] for BV integration
		"**************/32", // KKStream Kaohsiung office1
		"**************/32", // KKStream Kaohsiung office2
		"***********/32",    // BV DRM server dev env // TODO remove after BV DRM PROD server is ready
		"*************/32",  // BV DRM server staging env // TODO remove after BV DRM PROD server is ready
		"************/32",   // BV DRM server prod env
		"************/32",   // BV DRM server prod env
		"*************/32",  // BV DRM server prod env
		//[END]
	}
	for _, cidrBlock := range internalNetworks {
		_, cidr, _ := net.ParseCIDR(cidrBlock)
		cidrs = append(cidrs, cidr)
	}

	drmServerIPFilter = common.Append(
		kkmiddleware.IPWhiteList(cidrs),
	)

	getFileSystem := func(dist embed.FS, dirName string) http.FileSystem {
		fsys, err := fs.Sub(dist, dirName)
		if err != nil {
			log.Fatal(err)
		}
		return http.FS(fsys)
	}

	switch config.Env {
	case "test":
		gplusLoginCallBack = "https://test-api.kktv.com.tw/v3/auth/console/callback?provider=google"
	case "stag", "stage":
		gplusLoginCallBack = "https://staging-api.kktv.com.tw/v3/auth/console/callback?provider=google"
	case "prod":
		gplusLoginCallBack = "https://api.kktv.com.tw/v3/auth/console/callback?provider=google"
	}

	// local development if using MAC
	if runtime.GOOS == "darwin" {
		gplusLoginCallBack = "http://localhost:8080/v3/auth/console/callback?provider=google"
	}

	auditForUser := alice.New(auditor.LogByUser)
	//auditForConsole := alice.New(auditor.LogByConsole)

	// google login
	goth.UseProviders(
		google.New(config.GoogleAppID, config.GoogleAppSecret, gplusLoginCallBack, "email", "profile"),
	)

	// OPTIONS
	mux.Options("/*", common.ThenFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	v4Handlers := v4.NewHandlers()
	v3TitlelistHandler := v3Titlelist.NewHandler()
	v3AdsHandler := v3Ads.NewHandler()
	v3RedeemHandler := v3Redeem.NewHandler()
	v3ManifestHandler := v3Manifest.NewHandler()
	legacyBillingHandler := billing.NewHandler()
	v0AuthHandler := v0Auth.NewHandler()
	vendorHandlers := vendors.NewHandlers()

	// auth
	mux.Post("/v0/auth/mod", common.Then(auditForUser.ThenFunc(v0AuthHandler.MODLogin)))
	mux.Get("/v4/mod/products", common.ThenFunc(v4Handlers.MOD.GetMODProducts))
	/////////////////////////////// dashboard console start
	// logging
	mux.Get("/v3/console/auditlog", console.ThenFunc(kkconsole.GetConsoleAuditLog))

	// login
	mux.Get("/v3/auth/console/login", common.ThenFunc(gothic.BeginAuthHandler))
	mux.Get("/v3/auth/console/callback", common.ThenFunc(kkconsole.UserCallBack))
	mux.Post("/v3/auth/console/access_token", common.ThenFunc(kkconsole.ExchangeAccessToken))
	mux.Get("/v3/console/user/me", console.ThenFunc(kkconsole.GetConsoleMe))

	// user
	cmsHandlers := consoleapi.NewHandlers(config.Env, container.DBPoolUser(), container.DBPoolMeta(), container.CachePoolMeta())
	mux.Get("/v3/console/user", console.ThenFunc(cmsHandlers.User.LegacySearch))
	mux.Get("/v3/console/user/member_roles", console.ThenFunc(cmsHandlers.User.GetMemberRoles))
	mux.Get("/v3/console/user/:id", console.ThenFunc(cmsHandlers.User.Get))
	mux.Post("/v3/console/users:search", console.ThenFunc(cmsHandlers.User.Search))
	mux.Put("/v3/console/V2user/:id", console.ThenFunc(cmsHandlers.User.Put))
	mux.Put("/v3/console/user/:id/paymentinfo", console.ThenFunc(cmsHandlers.User.PutPaymentInfo))
	mux.Get("/v3/console/userchangelog", console.ThenFunc(kkconsole.GetConsoleUserChangeLog))
	mux.Get("/v3/console/userinfo", console.ThenFunc(kkconsole.GetConsoleUserInfo))
	mux.Get("/v3/console/user_receipt", console.ThenFunc(kkconsole.GetConsoleUserReceipt))
	mux.Put("/v3/console/userreset", consoleAdmin.ThenFunc(kkconsole.PutConsoleUserReset))
	mux.Put("/v3/console/user/:userid", consoleUser.ThenFunc(kkconsole.PutConsoleUser))
	mux.Delete("/v3/console/user/:userid", consoleUser.ThenFunc(kkconsole.DeleteConsoleUser))
	mux.Get("/v3/console/family", console.ThenFunc(kkconsole.GetConsoleFamily))
	mux.Get("/v3/console/order", console.ThenFunc(kkconsole.GetConsoleOrder))
	mux.Get("/v3/console/invoice", console.ThenFunc(kkconsole.GetConsoleInvoice))
	mux.Put("/v3/console/order", consoleUser.ThenFunc(kkconsole.PutConsoleOrder))
	mux.Post("/v3/console/order/unsubscribe", consoleUser.ThenFunc(kkconsole.PostConsoleOrderUnsubscribe))
	mux.Get("/v3/console/token", console.ThenFunc(kkconsole.GetConsoleToken))
	mux.Get("/v3/console/modorderstate", console.ThenFunc(kkconsole.GetMODOrderState))
	mux.Put("/v3/console/token", consoleAdmin.ThenFunc(kkconsole.PutConsoleToken))
	mux.Put("/v3/console/device/token", consoleUser.ThenFunc(kkconsole.PutConsoleDeviceAllToken))
	mux.Get("/v3/console/user/favorite/:userid", console.ThenFunc(kkconsole.GetConsoleUserFavorite))
	mux.Delete("/v3/console/user/favorite/:userid/:titleid", consoleUser.ThenFunc(kkconsole.DelConsoleUserFavorite))
	mux.Get("/v3/console/user/watch_history/:userid", console.ThenFunc(kkconsole.GetConsoleUserWatchHistory))
	mux.Delete("/v3/console/user/watch_history/:userid/:titleid", consoleUser.ThenFunc(kkconsole.DelConsoleUserWatchHistory))
	mux.Get("/v3/console/user/:userid/audit_log", consoleUser.ThenFunc(kkconsole.GetConsoleUserAuditLog))
	mux.Get("/v3/console/order/:orderid/audit_log", consoleUser.ThenFunc(kkconsole.GetConsoleOrderAuditLog))
	mux.Post("/v3/console/user/roles", consoleAdmin.ThenFunc(kkconsole.PostUserRoles))
	mux.Get("/v3/console/user/roles", consoleAdmin.ThenFunc(kkconsole.GetUserRoles))
	mux.Get("/v3/console/roles", consoleAdmin.ThenFunc(kkconsole.GetAllRoles))
	mux.Delete("/v3/console/user/roles", consoleAdmin.ThenFunc(kkconsole.DelUserGroups))

	// product
	mux.Get("/v3/console/product", console.ThenFunc(kkconsole.GetConsoleProduct))
	mux.Get("/v3/console/billing_product", console.ThenFunc(kkconsole.GetConsoleBillingProduct))
	mux.Post("/v3/console/product", consoleProduct.ThenFunc(kkconsole.PostConsoleProduct))
	mux.Post("/v3/console/productimage", consoleProduct.ThenFunc(kkconsole.PostConsoleProductImage))

	// packages
	mux.Get("/v3/console/packages", console.ThenFunc(cmsHandlers.ProductPackage.ListAll))
	mux.Post("/v3/console/packages", consoleProduct.ThenFunc(cmsHandlers.ProductPackage.CreatePackage))
	mux.Put("/v3/console/packages/:productPackageID", consoleProduct.ThenFunc(cmsHandlers.ProductPackage.Update))
	mux.Post("/v3/console/packages/:productPackageID/images:upload", consoleProduct.ThenFunc(kkconsole.UploadConsoleProductPackageImages))
	mux.Get("/v3/console/package/categories", console.ThenFunc(kkconsole.GetConsolePackageCategory))
	mux.Post("/v3/console/package/category", console.ThenFunc(kkconsole.PostConsolePackageCategory))

	// redeem
	mux.Get("/v3/console/redeem", console.ThenFunc(kkconsole.GetConsoleRedeem))
	mux.Post("/v3/console/redeem", consoleRedeem.ThenFunc(kkredeem.PostCouponGroup))
	mux.Get("/v3/console/redeemdetail", console.ThenFunc(kkconsole.GetConsoleRedeemDetail))
	mux.Put("/v3/console/redeemdetail/code/:id/:status", console.ThenFunc(kkconsole.ToggleConsoleRedeemCodeStatus))
	mux.Put("/v3/console/redeemdetail/group/:id/:status", console.ThenFunc(kkconsole.ToggleConsoleRedeemCodeStatusByGroup))

	// service status
	mux.Put("/v3/console/service_status", consoleAdmin.ThenFunc(kkconsole.PutConsoleServiceStatus))

	// remote config
	mux.Put("/v3/console/remote_config", consoleContent.ThenFunc(kkconsole.PutConsoleRemoteConfig))
	mux.Get("/v3/console/remote_config", consoleContent.ThenFunc(kkconsole.GetConsoleRemoteConfig))

	// meta
	mux.Get("/v3/console/title", console.ThenFunc(kkconsole.GetConsoleMetaTitle))
	mux.Put("/v3/console/title/:titleid", consoleContent.ThenFunc(kkconsole.PutConsoleMetaTitle))
	mux.Get("/v3/console/titlehint", console.ThenFunc(kkconsole.GetConsoleMetaTitleHint))
	mux.Get("/v3/console/series", console.ThenFunc(kkconsole.GetConsoleMetaSeries))
	mux.Get("/v3/console/extra", console.ThenFunc(kkconsole.GetConsoleMetaExtra))
	mux.Get("/v3/console/extrahint", console.ThenFunc(kkconsole.GetConsoleMetaExtraHint))
	mux.Put("/v3/console/extra/:titleid/:extraid", consoleContent.ThenFunc(kkconsole.PutConsoleMetaExtra))
	mux.Delete("/v3/console/extra/:titleid/:extraid", consoleContent.ThenFunc(kkconsole.DelConsoleMetaExtra))
	mux.Post("/v3/console/title/release-info:interpret", consoleContent.ThenFunc(cmsHandlers.Title.InterpretReleaseInfo))
	mux.Post("/v3/console/title/release-info/syntax", consoleContent.ThenFunc(cmsHandlers.Title.ReleaseInfoToSyntax))

	// titlelist
	mux.Get("/v3/console/titlelist", console.ThenFunc(kkconsole.GetConsoleAllTitleList))
	mux.Get("/v3/console/titlelist/:titlekey", console.ThenFunc(kkconsole.GetConsoleTitleListDetail))
	mux.Put("/v3/console/titlelist/:titlekey", consoleContent.ThenFunc(kkconsole.PutConsoleTitleList))
	mux.Get("/v3/console/default_titlelist", consoleContent.ThenFunc(kkconsole.GetConsoleDefaultTitleList))
	mux.Post("/v3/console/default_titlelist", consoleContent.ThenFunc(kkconsole.PostConsoleDefaultTitleList))
	mux.Put("/v3/console/titlelist_shareId", consoleContent.ThenFunc(kkconsole.PutConsoleTitleListShareId))

	// meta_titlelist
	mux.Get("/v3/console/metatitlelist", console.ThenFunc(kkconsole.GetConsoleMetaTitleList))
	mux.Post("/v3/console/metatitlelist", consoleContent.ThenFunc(cmsHandlers.TitleList.Create))
	mux.Put("/v3/console/metatitlelist", consoleContent.ThenFunc(cmsHandlers.TitleList.Update))
	mux.Delete("/v3/console/metatitlelist", consoleContent.ThenFunc(kkconsole.DelConsoleMetaTitleList))
	mux.Post("/v3/console/metatitlelistimage", consoleContent.ThenFunc(kkconsole.PostConsoleMetaTitleListImage))
	mux.Post("/v3/console/titlelist_meta_image", consoleContent.ThenFunc(kkconsole.PostConsoleTitleListMetaImage))

	// series episode
	mux.Put("/v3/console/episode", consoleContent.ThenFunc(kkconsole.PutConsoleEpisode))
	mux.Put("/v3/console/episode_publish", consoleContent.ThenFunc(kkconsole.PutConsoleEpisodePublish))
	mux.Post("/v3/console/episode/:id/invalidation", consoleContent.ThenFunc(kkconsole.PostConsoleEpisodeInvalidation))

	// publish episode
	mux.Get("/v3/console/publish", console.ThenFunc(kkconsole.GetConsolePublish))
	mux.Put("/v3/console/publish", consoleContent.ThenFunc(kkconsole.PutConsolePublish))
	mux.Delete("/v3/console/episodes/publish-time", consoleContent.ThenFunc(cmsHandlers.Title.DeleteEpisodePublishTime))

	// announce
	mux.Get("/v3/console/announce", console.ThenFunc(kkconsole.GetConsoleAnnounce))
	mux.Put("/v3/console/announce", consoleContent.ThenFunc(kkconsole.PutConsoleAnnounce))

	// event
	mux.Post("/v3/console/eventimage", consoleContent.ThenFunc(kkconsole.PostConsoleEventImage))
	mux.Delete("/v3/console/eventimage", consoleContent.ThenFunc(kkconsole.DelConsoleEventImage))
	mux.Get("/v3/console/event", console.ThenFunc(kkconsole.GetConsoleEvent))
	mux.Get("/v3/console/event/list", console.ThenFunc(cmsHandlers.Event.List))
	mux.Put("/v3/console/event", consoleContent.ThenFunc(cmsHandlers.Event.Update))

	// headline
	mux.Get("/v3/console/headline", console.ThenFunc(kkconsole.GetConsoleHeadline))
	mux.Post("/v3/console/headline", consoleContent.ThenFunc(kkconsole.PostConsoleHeadline))
	mux.Delete("/v3/console/headline", consoleContent.ThenFunc(kkconsole.DelConsoleHeadline))
	mux.Post("/v3/console/headlineimage", consoleContent.ThenFunc(kkconsole.PostConsoleHeadlineImage))

	// browse
	mux.Get("/v3/console/browse", console.ThenFunc(kkconsole.GetConsoleBrowse))
	mux.Post("/v3/console/browse", consoleContent.ThenFunc(kkconsole.PostConsoleBrowse))
	mux.Put("/v3/console/browse", consoleContent.ThenFunc(kkconsole.PutConsoleBrowse))
	mux.Delete("/v3/console/browse", consoleContent.ThenFunc(kkconsole.DelConsoleBrowse))
	mux.Post("/v3/console/browseimage", consoleContent.ThenFunc(kkconsole.PostConsoleBrowseImage))

	// ads
	mux.Get("/v3/console/ads", console.ThenFunc(cmsHandlers.Ads.List))
	mux.Put("/v3/console/ads", consoleContent.ThenFunc(cmsHandlers.Ads.Update))
	mux.Post("/v3/console/ads_image", consoleContent.ThenFunc(kkconsole.PostConsoleAdsImage))
	mux.Delete("/v3/console/ads_image", consoleContent.ThenFunc(kkconsole.DelConsoleAdsImage))

	// hotkeyword
	mux.Put("/v3/console/hotkeyword", consoleContent.ThenFunc(kkconsole.PutConsoleHotKeyWord))

	// encoder
	mux.Get("/v3/console/encoder", console.ThenFunc(kkconsole.GetConsoleEncoder))

	// encoder - BV
	mux.Get("/v3/console/encoderBV", console.ThenFunc(kkconsole.GetConsoleEncoderBV))
	mux.Delete("/v3/console/encoderBV", consoleContent.ThenFunc(kkconsole.DelConsoleEncoderBV))

	// TV Events
	mux.Get("/v3/console/tvevents", consoleContent.ThenFunc(kkconsole.GetConsoleTVEvents))
	mux.Post("/v3/console/tvevents", consoleContent.ThenFunc(kkconsole.PostConsoleTVEvents))
	mux.Get("/v3/console/tvevents_coupon_groups", consoleContent.ThenFunc(kkconsole.GetConsoleCouponGroups))

	// finance
	mux.Get("/v3/console/finance/income", consoleFinance.ThenFunc(kkconsole.GetConsoleIncomeReport))

	// addroid devices
	mux.Get("/v3/console/android/devices", consoleUser.ThenFunc(kkconsole.GetConsoleAndroidDevice))
	mux.Put("/v3/console/android/devices", consoleUser.ThenFunc(kkconsole.PutConsoleAndroidDevice))

	/////////////////////////////// dashboard console end

	// common
	mux.Get("/v3/browse", common.ThenFunc(kkhandler.GetBrowse))
	mux.Get("/v3/chart", common.ThenFunc(kkhandler.GetChart))
	mux.Get("/v3/collections", common.ThenFunc(kksearch.GetBrowseCollections))
	mux.Get("/v3/promotions", common.ThenFunc(kkhandler.GetPromotion))
	mux.Get("/v3/highlights", common.ThenFunc(kkhandler.GetHighlight))
	mux.Get("/v3/headlines", guestAuthed.ThenFunc(kkhandler.GetHeadlines))
	mux.Get("/v3/hot_titles", common.ThenFunc(kkhandler.GetHotTitles))

	// `youwilllove` is special case for titlelist, it's not in the `titlelist` table but from the cold-start recommendation
	mux.Get("/v3/title_list/youwilllove", identified.ThenFunc(v3TitlelistHandler.GetColdStartRecommends))
	mux.Get("/v3/title_list/#shareID"+titleListExpireSoonRegex, guestAuthed.ThenFunc(v3TitlelistHandler.GetExpireSoon))
	mux.Get("/v3/title_list/:shareID", guestAuthed.ThenFunc(v3TitlelistHandler.GetByShareID))

	mux.Get("/v3/hot_keywords", common.ThenFunc(kkhandler.GetHotkeyWord))
	mux.Get("/v3/product_packages", common.ThenFunc(kkhandler.ListProductPackage))
	mux.Get("/v4/product_packages", guestAuthed.ThenFunc(v4Handlers.ProductPkg.GetProductPackages))
	mux.Get("/v3/primestatus", common.ThenFunc(kkbilling.GetPrimeStatus))
	mux.Get("/v3/ipcheck", common.ThenFunc(kkhandler.GetIPCheck))

	mux.Get("/v3/auth/token", common.ThenFunc(kkauth.GetAuth))
	mux.Get("/v3/auth/guest", common.ThenFunc(kkauth.GetAuthGuest))
	mux.Post("/v3/auth/refresh_token", common.ThenFunc(kkauth.PostRefreshToken))

	mux.Get("/v3/android/devices", common.ThenFunc(kkhandler.GetAndroidDevice))

	// campaign
	mux.Get("/v3/campaigns/:year/:permanent_link", common.ThenFunc(kkhandler.GetCampaign))

	// sitemap
	mux.Get("/v3/sitemap/:type", common.ThenFunc(kkhandler.GetSitemap))

	// meta for SEO
	mux.Get("/v4/seo/titles/:titleId", common.ThenFunc(v4Handlers.Seo.GetTitleMeta))
	mux.Get("/v4/seo/videos", common.ThenFunc(v4Handlers.Seo.GetAvailableTitles))

	v4DeviceTypePath := "/v4/#deviceType^[aw]$" // a: app, w: web
	// PAGE
	mux.Get(v4DeviceTypePath+"/browses", hideLust.Then(common.ThenFunc(v4Handlers.Page.ListBrowses)))
	mux.Get(v4DeviceTypePath+"/featured_pages/:browseKey", gziphandler.GzipHandler(guestAuthed.ThenFunc(v4Handlers.Page.GetFeaturedContent)))
	// TITLE
	const titleIDRegex = `#titleID^\d{8}$`
	mux.Get(v4DeviceTypePath+`/titles/`+titleIDRegex, guestAuthed.ThenFunc(v4Handlers.Title.GetByID))
	mux.Get(v4DeviceTypePath+`/titles/`+titleIDRegex+`/related-titles`, authed.ThenFunc(v4Handlers.Title.ListRelatedTitles))
	// COLLECTION
	mux.Get(v4DeviceTypePath+"/collections/expire-soon", authed.ThenFunc(v4Handlers.Collection.GetExpireSoonTitles))
	mux.Get(v4DeviceTypePath+"/collections/:collection", authed.Then(hideLust.ThenFunc(v4Handlers.Collection.Query)))
	// SEARCH
	mux.Get(v4DeviceTypePath+"/search/:keyword", authed.Then(hideLust.ThenFunc(v4Handlers.Search.KeywordSearch)))
	// WATCH HISTORY
	mux.Get(v4DeviceTypePath+"/users/me/watch-history", memberAuthed.ThenFunc(v4Handlers.WatchHistory.GetByUser))
	// FAVORITE
	mux.Get(v4DeviceTypePath+"/users/me/favorite-titles", memberAuthed.ThenFunc(v4Handlers.Favorite.GetMyFavoriteTitles))
	mux.Get(v4DeviceTypePath+"/users/me/favorite-explorer", gziphandler.GzipHandler(memberAuthed.ThenFunc(v4Handlers.Favorite.ExploreMyFavorite)))
	mux.Delete("/v4/users/me/favorite-titles", memberAuthed.ThenFunc(v4Handlers.Favorite.DeleteMyFavoriteTitles))
	mux.Post("/v4/users/me/favorite-titles", memberAuthed.ThenFunc(v4Handlers.Favorite.AddMyFavoriteTitles))
	mux.Patch("/v4/users/me/favorite-titles", memberAuthed.ThenFunc(v4Handlers.Favorite.UpdateMyFavoriteTitles))
	mux.Get("/v4/users/me/favorite-titles/ids", gziphandler.GzipHandler(memberAuthed.ThenFunc(v4Handlers.Favorite.GetMyFavoriteTitleIDs)))
	// TITLE_LIST
	mux.Get(v4DeviceTypePath+"/title-lists/anime-airing", guestAuthed.ThenFunc(v4Handlers.Titlelist.GetAiringAnime))
	mux.Get(v4DeviceTypePath+"/title-lists/youwilllove", identified.ThenFunc(v4Handlers.Titlelist.GetColdStartRecommends))
	mux.Get(v4DeviceTypePath+"/title-lists/#shareID"+titleListExpireSoonRegex, guestAuthed.ThenFunc(v4Handlers.Titlelist.GetExpireSoon))
	mux.Get(v4DeviceTypePath+"/title-lists/:shareID", guestAuthed.ThenFunc(v4Handlers.Titlelist.GetByShareID))
	mux.Get(v4DeviceTypePath+"/picks/:shareID", guestAuthed.ThenFunc(v4Handlers.Titlelist.GetHighlightByShareID))
	// COLD_START
	mux.Get("/v4/cold-start/content-preferences", gziphandler.GzipHandler(identified.Then(hideLust.ThenFunc(v4Handlers.ColdStart.GetContentPreferences))))
	mux.Post("/v4/users/me/content-preferences", gziphandler.GzipHandler(identified.ThenFunc(v4Handlers.ColdStart.UpdateUserPreference)))
	// BILLING
	mux.Put("/v4/billing/credit_card", authed.ThenFunc(v4Handlers.Billing.PutCreditCard))
	// REMOTE_CONFIG
	mux.Get("/v4/users/me/remote-configs", identified.ThenFunc(v4Handlers.RemoteConfig.GetByUser))
	// TEXT_CONTENT
	mux.Get("/v4/text-contents", gziphandler.GzipHandler(guestAuthed.ThenFunc(v4Handlers.TextContent.Get)))
	// AUTH
	mux.Get("/v4/auth/redirect", authed.ThenFunc(v4Handlers.Auth.SignRedirectURI))
	mux.Post("/v4/oauth/auth-code", authed.ThenFunc(v4Handlers.Auth.CreateAuthCode))
	mux.Post("/v4/oauth/access-token", basicAuth.ThenFunc(v4Handlers.Auth.ExchangeAccessToken))
	// LIBRARY
	mux.Get("/v4/library/titles", guestAuthed.ThenFunc(v4Handlers.Library.ListTitles))
	// health
	mux.Get("/v3/health", common.ThenFunc(kkhandler.GetHealth))

	if config.Debug {
		// load internal-tokens only in Debug mode
		mux.Post("/v3/auth/internal-tokens", common.ThenFunc(kkauth.PostInternalTokens))
	}

	// device auth for android TV
	mux.Get("/v3/device/register", common.ThenFunc(kkauth.GetDeviceRegister))
	mux.Get("/v3/device/pairing", common.ThenFunc(kkauth.GetDevicePairing))

	// kktv youtube
	mux.Get("/v3/yt", gziphandler.GzipHandler(common.ThenFunc(youtube.GetYT)))

	// kkid revoke
	mux.Post("/v3/kkid/revoke", common.ThenFunc(kkauth.PostRevokeKKID))

	// common geolocation blocked
	mux.Get("/v3/service_status", commongeo.ThenFunc(kkhandler.GetServiceStatus))

	// common remote config
	mux.Get("/v3/remote_config", commongeo.ThenFunc(kkhandler.GetRemoteConfig))

	// guest auth
	mux.Get("/v3/titles/:titleid", guestAuthed.ThenFunc(kkhandler.GetTitle))
	mux.Get("/v3/pages/featured", gziphandler.GzipHandler(guestAuthed.ThenFunc(kkhandler.GetFeatured)))
	mux.Get("/v3/event", guestAuthed.ThenFunc(kkhandler.GetEvent))
	mux.Get("/v3/ads", guestAuthed.ThenFunc(v3AdsHandler.List))

	// auth
	// manifests
	mux.Get("/v3/titles/:titleid/episodes/:episodeids/mezzanine/manifests", authed.ThenFunc(v3ManifestHandler.GetManifest))

	// device auth for android/apple TV
	mux.Get("/v3/device/grant", authed.ThenFunc(kkauth.GetDeviceGrant))
	mux.Get("/v3/titles/:titleid/related_titles", authed.ThenFunc(kkhandler.GetRelatedTitle))
	mux.Get("/v3/titles/:titleid/related_titles_rdc", authed.ThenFunc(kkhandler.GetRelatedTitle))
	mux.Get("/v3/auth/signout", authed.ThenFunc(kkauth.GetSignout))
	mux.Get("/v3/auth/verify_token", authed.ThenFunc(kkauth.GetVerifyToken))
	mux.Get("/v3/auth/bind", authed.ThenFunc(kkauth.GetBindKKBOX))
	mux.Get("/v3/auth/kkidbind", authed.ThenFunc(kkauth.GetBindKKID))
	mux.Put("/v3/auth/kkbox", authed.ThenFunc(kkauth.PutKKBOX))
	mux.Get("/v3/collections/:collection", authed.Then(hideLust.ThenFunc(kksearch.GetCollections)))
	mux.Get("/v3/prime_redeem", authed.ThenFunc(kkhandler.GetKKPrimeRedeem))
	mux.Get("/v3/search/:query", authed.Then(hideLust.ThenFunc(kksearch.GetSearch)))
	mux.Get("/v3/search_suggestions/:query", authed.ThenFunc(kksearch.GetSearchSuggestions))

	// user
	mux.Get("/v3/users/me", authed.ThenFunc(kkhandler.GetMe))
	mux.Delete("/v3/users/me", authed.ThenFunc(kkhandler.DeleteMe))
	mux.Get("/v3/users/me/search_history", authed.ThenFunc(kksearch.GetSearchHistory))
	mux.Delete("/v3/users/me/search_history", authed.ThenFunc(kksearch.DelSearchHistory))
	mux.Put("/v3/users/me/unsubscribe", authed.ThenFunc(kkhandler.Unsubscribe))
	mux.Get("/v3/users/me/orders", authed.ThenFunc(kkhandler.GetOrders))

	mux.Post("/v4/users/otp", guestAuthed.ThenFunc(v4Handlers.User.GenerateOTP))
	mux.Post("/v4/users/otp/verify", guestAuthed.ThenFunc(v4Handlers.User.VerifyOTP))
	mux.Put("/v4/users/me/password/set", authedWithoutLogger.Then(auditForUser.ThenFunc(v4Handlers.User.SetPassword)))
	mux.Put("/v4/users/me/password", authedWithoutLogger.Then(auditForUser.ThenFunc(v4Handlers.User.UpdatePassword)))
	mux.Post("/v4/users/account/verify", guestAuthed.ThenFunc(v4Handlers.User.VerifyAccount))
	mux.Put("/v4/users/me/account/set", memberAuthed.Then(auditForUser.ThenFunc(v4Handlers.User.SetAccount)))
	mux.Put("/v4/users/me/account", authed.Then(auditForUser.ThenFunc(v4Handlers.User.UpdateAccount)))
	mux.Post("/v4/users/sign-up", commonWithoutLogger.Then(auditForUser.ThenFunc(v4Handlers.User.SignUp)))
	mux.Post("/v4/users/login", commonWithoutLogger.ThenFunc(v4Handlers.User.Login))
	mux.Post("/v4/users/password/reset", guestAuthed.ThenFunc(v4Handlers.User.VerifyPasswordResetAccount))
	mux.Put("/v4/users/password", commonWithoutLogger.Then(auditForUser.ThenFunc(v4Handlers.User.PasswordReset)))
	mux.Post("/v4/users/me/password/verify", authedWithoutLogger.ThenFunc(v4Handlers.User.VerifyPassword))
	mux.Delete("/v4/users/me/subscription", authed.ThenFunc(v4Handlers.User.CancelSubscription))
	mux.Post("/v4/users/me/survey/:identifier", authed.Then(auditForUser.ThenFunc(v4Handlers.User.SubmitSurvey)))
	mux.Get("/v4/users/me/memberships", authed.ThenFunc(v4Handlers.User.GetMyMemberships))
	mux.Get("/v4/users/me/service", memberAuthed.ThenFunc(v4Handlers.User.GetMyProductService))
	mux.Get("/v4/users/me", memberAuthed.ThenFunc(v4Handlers.User.GetMe))

	// for kkbox cs
	mux.Get("/v3/users/status", common.ThenFunc(kkhandler.GetUserStatus))

	// family
	mux.Post("/v3/auth/family", memberAuthed.ThenFunc(kkauth.PostFamily))
	mux.Post("/v3/users/me/invite", authed.ThenFunc(kkhandler.PostInvite))
	mux.Get("/v3/users/me/family", authed.ThenFunc(kkhandler.GetFamily))
	mux.Delete("/v3/users/me/family", memberAuthed.ThenFunc(kkhandler.DelFamily))

	// watchhostory
	mux.Get("/v3/users/me/watch_history", authed.ThenFunc(kkhandler.GetMeWatchHistory))
	mux.Delete("/v3/users/me/watch_history/:titleid", authed.ThenFunc(kkhandler.DelMeWatchHistory))

	// favorite_titles
	mux.Get("/v3/users/me/favorite_titles", authed.ThenFunc(kkhandler.GetMeFavorite))
	mux.Post("/v3/users/me/favorite_titles/:titleid", authed.ThenFunc(kkhandler.PostMeFavorite))
	mux.Delete("/v3/users/me/favorite_titles/:titleid", authed.ThenFunc(kkhandler.DelMeFavorite))

	// last_played
	mux.Get("/v3/users/me/last_played/:titleid", authed.ThenFunc(kkhandler.GetMeLastPlayed))
	mux.Put("/v3/users/me/last_played/:titleid", authed.ThenFunc(kkhandler.PutMeLastPlayed))

	// title rating
	mux.Post("/v3/users/me/title_rating", authed.ThenFunc(kkhandler.PostMeTitleRating))

	// childlock
	mux.Put("/v3/users/me/childlock", authed.ThenFunc(kkhandler.PutChildLock))
	mux.Post("/v3/users/me/childlock", authed.ThenFunc(kkhandler.PostChildLock))

	// compensate
	mux.Post("/v3/users/me/compensate", authed.ThenFunc(kkhandler.PostMeCompensate))

	// recommend for DEBUG
	mux.Get("/v3/users/me/recommend", authed.ThenFunc(kkhandler.GetMeRecommend))

	// playback token && heartbeat
	mux.Post("/v3/playback_tokens", authedgeo.ThenFunc(kkhandler.PostPlaybackToken))
	mux.Post("/v3/heartbeat", authedWithoutLogger.ThenFunc(kkhandler.PostHeartbeat))

	// payment
	mux.Post("/v3/payment/iab", memberAuthed.ThenFunc(kkpayment.PostIAB))
	mux.Post("/v3/payment/iap", memberAuthed.ThenFunc(kkpayment.PostIAP))
	mux.Post("/v3/payment/creditcard", memberAuthed.ThenFunc(kkpayment.PostCreditCard))
	mux.Post("/v3/payment/cnsca", memberAuthed.ThenFunc(kkpayment.PostCNS))
	mux.Put("/v3/payment/creditcard", authed.ThenFunc(kkpayment.PutCreditCard))

	// billing webhooks
	mux.Post("/v3/payment/bapi", common.ThenFunc(kkpayment.BillingSubscriptionChangedWebhook))
	mux.Post("/v3/billing/webhooks/subscription_changed", common.ThenFunc(kkpayment.BillingSubscriptionChangedWebhook))
	mux.Post("/v4/billing/webhooks/order_changed", common.ThenFunc(v4Handlers.Billing.OrderChangedWebhook))
	mux.Post("/v4/billing/webhooks/products_updated", common.ThenFunc(v4Handlers.Billing.ProductsUpdatedWebhook))

	// remember api doc
	mux.Get("/v3/payment/orders/:order_number", authed.ThenFunc(billing.GetOrder))
	mux.Post("/v3/payment/billing/orders", memberAuthed.ThenFunc(legacyBillingHandler.PostBillingOrders))
	mux.Post("/v3/payment/billing/orders/grace-period", memberAuthed.ThenFunc(legacyBillingHandler.PurchaseForGracePeriod))
	mux.Post("/v3/payment/billing/redeem_codes/:redeemCode", authed.ThenFunc(billing.PostBillingRedeemsCodes))

	// redeem
	mux.Post("/v3/redeem_verify", authed.ThenFunc(kkpayment.VerifyCouponCode))
	mux.Post("/v3/redeem/:redeem_code", authed.ThenFunc(v3RedeemHandler.RedeemCode))

	// TODO remove use /users/me
	mux.Get("/v3/users/:userid", authed.ThenFunc(kkhandler.GetMe))
	mux.Get("/v3/search_history", authed.ThenFunc(kksearch.GetSearchHistory))
	mux.Delete("/v3/search_history", authed.ThenFunc(kksearch.DelSearchHistory))
	// TODO end

	// api doc
	if config.Env != "prod" {
		jwtAuthen := pkgauth.NewJWTAuth(
			jwt.SigningMethodHS256,
			[]byte("kktv-api-doc"), //TODO move to config
		)

		googleAuthProvider := google.New(config.GoogleAppID, config.GoogleAppSecret, config.APIDocOAuthCallback, "email")
		goth.UseProviders(
			googleAuthProvider,
		)

		docHandler := apidoc.NewHandler(jwtAuthen, googleAuthProvider)
		// API doc auth routes
		mux.Get("/auth/doc/login", common.ThenFunc(gothic.BeginAuthHandler))
		mux.Get("/auth/doc/callback", common.ThenFunc(docHandler.HandleCallback))

		docHandleFunc := func(prefix, dirName string) http.Handler {
			return kkmiddleware.MaxAgeHandler(864000,
				gziphandler.GzipHandler(
					http.StripPrefix(prefix,
						http.FileServer(getFileSystem(docs.Static, dirName)))))
		}

		apiDocAuth := pmiddleware.NewAPIDocAuthenticator(jwtAuthen)
		const loginPath = apidoc.LoginURL
		mux.Get(loginPath, common.Then(docHandleFunc("/auth/doc/", "api/auth")))
		mux.Get("/v3/d0c/*", common.Then(
			apiDocAuth.Auth(
				docHandleFunc("/v3/d0c/", "api/swagger2"),
				loginPath+"?redirect_uri=/v3/d0c/",
			)))
		mux.Get("/doc/*", common.Then(
			apiDocAuth.Auth(
				docHandleFunc("/doc/", "api/swagger3"),
				loginPath+"?redirect_uri=/doc/",
			)))
	}
	mux.Get("/v3/console/*", kkmiddleware.MaxAgeHandler(864000, gziphandler.GzipHandler(http.StripPrefix("/v3/console/", http.FileServer(getFileSystem(web.Static, "console"))))))

	// TSTAR
	mux.Post("/api", thirdPartyIPFilter.ThenFunc(kkpayment.PostTSTAR))

	// internal api
	mux.Get("/v3/keys/:keyid", drmServerIPFilter.ThenFunc(kkdrm.GetKey))
	mux.Get("/v3/tokens/:tokenid", drmServerIPFilter.ThenFunc(kkdrm.GetToken))

	// Issue redeem codes
	mux.Post("/v3/issue_redeem", authed.ThenFunc(kkhandler.GetRedeemCode))

	// Apple App Store Server to Server notification
	mux.Post("/v3/iap_s2s_notification", common.ThenFunc(kkpayment.PostIAPS2S))

	// Google real-time developer notifications
	mux.Post("/v3/iab_s2s_notification", common.ThenFunc(kkpayment.PostIABS2S))

	// VENDORS
	//   FeverSocial
	mux.Post("/vendors/feversocial/get-access-token", common.ThenFunc(vendorHandlers.FeverSocial.GetAccessToken))
	mux.Get("/vendors/feversocial/get-user", basicAuth.ThenFunc(vendorHandlers.FeverSocial.GetUserByCode))
	mux.Get("/vendors/feversocial/get-membership-tier", jwtAuth.ThenFunc(vendorHandlers.FeverSocial.GetMembershipTier))
	//   LineTV
	mux.Post("/vendors/linetv/episodes/:episodeID/manifest", basicAuth.ThenFunc(vendorHandlers.LineTV.PostEpisodeManifest))

	// TRIALS for the fantasy project integration - only available in test environment
	if config.Env == "test" {
		mux.Get("/v4/trials/episodes/:episodeID/manifests", authed.ThenFunc(v3ManifestHandler.GetTrialEpisodeManifest))
		mux.Post("/v4/trials/playback/tokens", authed.ThenFunc(v4Handlers.Trial.CreatePlaybackToken))
	}

	if config.Debug {
		host := fmt.Sprintf(":%d", config.Port)
		go http.ListenAndServe(host, mux)
	}
	return mux
}

func initialize() {
	secret.Init(config.Env)

	container.RegisterMetaDB(config.DbMeta)
	container.RegisterUserDB(config.DbUser)
	container.RegisterRedeemDB(config.DbRedeem)
	container.RegisterMetaCache(config.RedisMeta)
	container.RegisterUserCache(config.RedisUser)

	auditor := auditing.NewRepository(container.DBPoolUser().Master())
	container.RegisterAuditingRepo(auditor)

	userCachePool := container.CachePoolUser()
	userCacheReader := cache.New(userCachePool.Slave())
	userCacheWriter := cache.New(userCachePool.Master())
	billingClient := pkgbilling.NewClient(config.BillingAPIHost, userCacheWriter, userCacheReader)
	container.RegisterBillingClient(billingClient)

	permissionService := permission.NewService(user.NewUserRepository(), billingClient)
	container.RegisterPermissionService(permissionService)
	orderService := order.NewService(billingClient, userCacheReader, userCacheWriter)
	container.RegisterOrderService(orderService)

	deeplink.Init(config.Env)
	image.Init(config.Env)
	zlog.Init(config.Env)
	ematic.Init(config.Env)
	if err := tracing.Initialize(kkapp.App.GetVersion()); err != nil {
		zlog.Warn("sentry init failed").Err(err).Send()
	}

	initializeEventListeners()
	// must be placed after config.init() and container registers to get the global kkapp.App content
	kkapp.ContextInit()

}

func initializeEventListeners() {
	userCachePool := container.CachePoolUser()
	userCacheReader := cache.New(userCachePool.Slave())
	userCacheWriter := cache.New(userCachePool.Master())
	userRepo := user.NewUserRepository()

	broadcaster := broadcasting.GetBroadcaster()

	userUpdatedLogger := auditing.NewUserUpdatedLogger(container.AuditingRepo(), userRepo, userCacheReader, userCacheWriter)
	broadcaster.Register(events.SignalUserUpdated, userUpdatedLogger)

	userWillBeUpdatedListener := auditing.NewUserWillBeUpdatedListener(userRepo, userCacheWriter)
	broadcaster.Register(events.SignalUserWillBeUpdated, userWillBeUpdatedListener)
}
